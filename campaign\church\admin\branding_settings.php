<?php
/**
 * Branding & White-label Settings
 * Complete branding customization and organization-specific terminology
 */

require_once 'includes/header.php';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'update_branding':
                    $brandingSettings = [
                        'organization_name' => $_POST['organization_name'],
                        'organization_type' => $_POST['organization_type'],
                        'tagline' => $_POST['tagline'],
                        'primary_color' => $_POST['primary_color'],
                        'secondary_color' => $_POST['secondary_color'],
                        'accent_color' => $_POST['accent_color'],
                        'font_family' => $_POST['font_family'],
                        'custom_css' => $_POST['custom_css'],
                        'favicon_url' => $_POST['favicon_url'],
                        'meta_description' => $_POST['meta_description'],
                        'meta_keywords' => $_POST['meta_keywords']
                    ];
                    
                    foreach ($brandingSettings as $key => $value) {
                        $stmt = $pdo->prepare("
                            INSERT INTO appearance_settings (setting_name, setting_value, updated_at)
                            VALUES (?, ?, NOW())
                            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()
                        ");
                        $stmt->execute([$key, $value]);
                    }
                    
                    $success_message = "Branding settings updated successfully!";
                    break;
                    
                case 'update_terminology':
                    $terminologySettings = [
                        'term_members' => $_POST['term_members'],
                        'term_member' => $_POST['term_member'],
                        'term_events' => $_POST['term_events'],
                        'term_event' => $_POST['term_event'],
                        'term_campaigns' => $_POST['term_campaigns'],
                        'term_campaign' => $_POST['term_campaign'],
                        'term_groups' => $_POST['term_groups'],
                        'term_group' => $_POST['term_group'],
                        'term_leaders' => $_POST['term_leaders'],
                        'term_leader' => $_POST['term_leader'],
                        'term_volunteers' => $_POST['term_volunteers'],
                        'term_volunteer' => $_POST['term_volunteer'],
                        'term_donations' => $_POST['term_donations'],
                        'term_donation' => $_POST['term_donation'],
                        'term_attendance' => $_POST['term_attendance'],
                        'term_ministry' => $_POST['term_ministry'],
                        'term_ministries' => $_POST['term_ministries']
                    ];
                    
                    foreach ($terminologySettings as $key => $value) {
                        $stmt = $pdo->prepare("
                            INSERT INTO appearance_settings (setting_name, setting_value, updated_at)
                            VALUES (?, ?, NOW())
                            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()
                        ");
                        $stmt->execute([$key, $value]);
                    }
                    
                    $success_message = "Terminology settings updated successfully!";
                    break;
                    
                case 'update_whitelabel':
                    $whitelabelSettings = [
                        'hide_powered_by' => isset($_POST['hide_powered_by']) ? 1 : 0,
                        'custom_footer_text' => $_POST['custom_footer_text'],
                        'support_email' => $_POST['support_email'],
                        'support_phone' => $_POST['support_phone'],
                        'support_url' => $_POST['support_url'],
                        'custom_domain' => $_POST['custom_domain'],
                        'ssl_enabled' => isset($_POST['ssl_enabled']) ? 1 : 0,
                        'google_analytics_id' => $_POST['google_analytics_id'],
                        'facebook_pixel_id' => $_POST['facebook_pixel_id'],
                        'custom_head_code' => $_POST['custom_head_code'],
                        'custom_body_code' => $_POST['custom_body_code']
                    ];
                    
                    foreach ($whitelabelSettings as $key => $value) {
                        $stmt = $pdo->prepare("
                            INSERT INTO appearance_settings (setting_name, setting_value, updated_at)
                            VALUES (?, ?, NOW())
                            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()
                        ");
                        $stmt->execute([$key, $value]);
                    }
                    
                    $success_message = "White-label settings updated successfully!";
                    break;
                    
                case 'reset_terminology':
                    $defaultTerms = [
                        'term_members' => 'Members',
                        'term_member' => 'Member',
                        'term_events' => 'Events',
                        'term_event' => 'Event',
                        'term_campaigns' => 'Campaigns',
                        'term_campaign' => 'Campaign',
                        'term_groups' => 'Groups',
                        'term_group' => 'Group',
                        'term_leaders' => 'Leaders',
                        'term_leader' => 'Leader',
                        'term_volunteers' => 'Volunteers',
                        'term_volunteer' => 'Volunteer',
                        'term_donations' => 'Donations',
                        'term_donation' => 'Donation',
                        'term_attendance' => 'Attendance',
                        'term_ministry' => 'Ministry',
                        'term_ministries' => 'Ministries'
                    ];
                    
                    foreach ($defaultTerms as $key => $value) {
                        $stmt = $pdo->prepare("
                            INSERT INTO appearance_settings (setting_name, setting_value, updated_at)
                            VALUES (?, ?, NOW())
                            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()
                        ");
                        $stmt->execute([$key, $value]);
                    }
                    
                    $success_message = "Terminology reset to defaults successfully!";
                    break;
            }
        }
    } catch (Exception $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}

// Get current settings
$stmt = $pdo->prepare("SELECT setting_name, setting_value FROM appearance_settings");
$stmt->execute();
$settings = [];
while ($row = $stmt->fetch()) {
    $settings[$row['setting_name']] = $row['setting_value'];
}

// Default values
$defaults = [
    'organization_name' => 'Church Management System',
    'organization_type' => 'church',
    'tagline' => 'Connecting Faith, Building Community',
    'term_members' => 'Members',
    'term_member' => 'Member',
    'term_events' => 'Events',
    'term_event' => 'Event',
    'term_campaigns' => 'Campaigns',
    'term_campaign' => 'Campaign',
    'term_groups' => 'Groups',
    'term_group' => 'Group',
    'term_leaders' => 'Leaders',
    'term_leader' => 'Leader',
    'term_volunteers' => 'Volunteers',
    'term_volunteer' => 'Volunteer',
    'term_donations' => 'Donations',
    'term_donation' => 'Donation',
    'term_attendance' => 'Attendance',
    'term_ministry' => 'Ministry',
    'term_ministries' => 'Ministries'
];

$page_title = "Branding & White-label Settings";
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-brush"></i> Branding & White-label Settings
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-outline-secondary" onclick="previewBranding()">
            <i class="bi bi-eye"></i> Preview Changes
        </button>
    </div>
</div>

<?php if (isset($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Navigation Tabs -->
<ul class="nav nav-tabs mb-4" id="brandingTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="branding-tab" data-bs-toggle="tab" data-bs-target="#branding" type="button" role="tab">
            <i class="bi bi-palette"></i> Branding
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="terminology-tab" data-bs-toggle="tab" data-bs-target="#terminology" type="button" role="tab">
            <i class="bi bi-chat-text"></i> Terminology
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="whitelabel-tab" data-bs-toggle="tab" data-bs-target="#whitelabel" type="button" role="tab">
            <i class="bi bi-shield-check"></i> White-label
        </button>
    </li>
</ul>

<div class="tab-content" id="brandingTabContent">
    <!-- Branding Tab -->
    <div class="tab-pane fade show active" id="branding" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-palette"></i> Organization Branding
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_branding">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="organization_name" class="form-label">Organization Name</label>
                            <input type="text" class="form-control" id="organization_name" name="organization_name" 
                                   value="<?php echo htmlspecialchars($settings['organization_name'] ?? $defaults['organization_name']); ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="organization_type" class="form-label">Organization Type</label>
                            <select class="form-select" id="organization_type" name="organization_type">
                                <option value="church" <?php echo ($settings['organization_type'] ?? 'church') === 'church' ? 'selected' : ''; ?>>Church</option>
                                <option value="nonprofit" <?php echo ($settings['organization_type'] ?? '') === 'nonprofit' ? 'selected' : ''; ?>>Non-profit</option>
                                <option value="ministry" <?php echo ($settings['organization_type'] ?? '') === 'ministry' ? 'selected' : ''; ?>>Ministry</option>
                                <option value="organization" <?php echo ($settings['organization_type'] ?? '') === 'organization' ? 'selected' : ''; ?>>Organization</option>
                                <option value="community" <?php echo ($settings['organization_type'] ?? '') === 'community' ? 'selected' : ''; ?>>Community</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="tagline" class="form-label">Tagline</label>
                        <input type="text" class="form-control" id="tagline" name="tagline" 
                               value="<?php echo htmlspecialchars($settings['tagline'] ?? $defaults['tagline']); ?>"
                               placeholder="A short description or motto">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="primary_color" class="form-label">Primary Color</label>
                            <div class="input-group">
                                <input type="color" class="form-control form-control-color" id="primary_color" name="primary_color" 
                                       value="<?php echo htmlspecialchars($settings['primary_color'] ?? '#007bff'); ?>">
                                <input type="text" class="form-control" value="<?php echo htmlspecialchars($settings['primary_color'] ?? '#007bff'); ?>" readonly>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="secondary_color" class="form-label">Secondary Color</label>
                            <div class="input-group">
                                <input type="color" class="form-control form-control-color" id="secondary_color" name="secondary_color" 
                                       value="<?php echo htmlspecialchars($settings['secondary_color'] ?? '#6c757d'); ?>">
                                <input type="text" class="form-control" value="<?php echo htmlspecialchars($settings['secondary_color'] ?? '#6c757d'); ?>" readonly>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="accent_color" class="form-label">Accent Color</label>
                            <div class="input-group">
                                <input type="color" class="form-control form-control-color" id="accent_color" name="accent_color" 
                                       value="<?php echo htmlspecialchars($settings['accent_color'] ?? '#28a745'); ?>">
                                <input type="text" class="form-control" value="<?php echo htmlspecialchars($settings['accent_color'] ?? '#28a745'); ?>" readonly>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="font_family" class="form-label">Font Family</label>
                            <select class="form-select" id="font_family" name="font_family">
                                <option value="system" <?php echo ($settings['font_family'] ?? 'system') === 'system' ? 'selected' : ''; ?>>System Default</option>
                                <option value="Arial" <?php echo ($settings['font_family'] ?? '') === 'Arial' ? 'selected' : ''; ?>>Arial</option>
                                <option value="Helvetica" <?php echo ($settings['font_family'] ?? '') === 'Helvetica' ? 'selected' : ''; ?>>Helvetica</option>
                                <option value="Georgia" <?php echo ($settings['font_family'] ?? '') === 'Georgia' ? 'selected' : ''; ?>>Georgia</option>
                                <option value="Times New Roman" <?php echo ($settings['font_family'] ?? '') === 'Times New Roman' ? 'selected' : ''; ?>>Times New Roman</option>
                                <option value="Roboto" <?php echo ($settings['font_family'] ?? '') === 'Roboto' ? 'selected' : ''; ?>>Roboto</option>
                                <option value="Open Sans" <?php echo ($settings['font_family'] ?? '') === 'Open Sans' ? 'selected' : ''; ?>>Open Sans</option>
                                <option value="Lato" <?php echo ($settings['font_family'] ?? '') === 'Lato' ? 'selected' : ''; ?>>Lato</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="favicon_url" class="form-label">Favicon URL</label>
                            <input type="url" class="form-control" id="favicon_url" name="favicon_url" 
                                   value="<?php echo htmlspecialchars($settings['favicon_url'] ?? ''); ?>"
                                   placeholder="https://example.com/favicon.ico">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="meta_description" class="form-label">Meta Description</label>
                        <textarea class="form-control" id="meta_description" name="meta_description" rows="2"
                                  placeholder="Brief description for search engines"><?php echo htmlspecialchars($settings['meta_description'] ?? ''); ?></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="meta_keywords" class="form-label">Meta Keywords</label>
                        <input type="text" class="form-control" id="meta_keywords" name="meta_keywords" 
                               value="<?php echo htmlspecialchars($settings['meta_keywords'] ?? ''); ?>"
                               placeholder="keyword1, keyword2, keyword3">
                    </div>
                    
                    <div class="mb-3">
                        <label for="custom_css" class="form-label">Custom CSS</label>
                        <textarea class="form-control" id="custom_css" name="custom_css" rows="6"
                                  placeholder="/* Add your custom CSS here */"><?php echo htmlspecialchars($settings['custom_css'] ?? ''); ?></textarea>
                        <div class="form-text">Add custom CSS to further customize the appearance</div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> Save Branding Settings
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Terminology Tab -->
    <div class="tab-pane fade" id="terminology" role="tabpanel">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-chat-text"></i> Custom Terminology
                </h5>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="reset_terminology">
                    <button type="submit" class="btn btn-outline-secondary btn-sm"
                            onclick="return confirm('Reset all terminology to defaults?')">
                        <i class="bi bi-arrow-clockwise"></i> Reset to Defaults
                    </button>
                </form>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    Customize the terminology used throughout the system to match your organization's language.
                </div>

                <form method="POST">
                    <input type="hidden" name="action" value="update_terminology">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="term_members" class="form-label">Members (Plural)</label>
                            <input type="text" class="form-control" id="term_members" name="term_members"
                                   value="<?php echo htmlspecialchars($settings['term_members'] ?? $defaults['term_members']); ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="term_member" class="form-label">Member (Singular)</label>
                            <input type="text" class="form-control" id="term_member" name="term_member"
                                   value="<?php echo htmlspecialchars($settings['term_member'] ?? $defaults['term_member']); ?>" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="term_events" class="form-label">Events (Plural)</label>
                            <input type="text" class="form-control" id="term_events" name="term_events"
                                   value="<?php echo htmlspecialchars($settings['term_events'] ?? $defaults['term_events']); ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="term_event" class="form-label">Event (Singular)</label>
                            <input type="text" class="form-control" id="term_event" name="term_event"
                                   value="<?php echo htmlspecialchars($settings['term_event'] ?? $defaults['term_event']); ?>" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="term_campaigns" class="form-label">Campaigns (Plural)</label>
                            <input type="text" class="form-control" id="term_campaigns" name="term_campaigns"
                                   value="<?php echo htmlspecialchars($settings['term_campaigns'] ?? $defaults['term_campaigns']); ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="term_campaign" class="form-label">Campaign (Singular)</label>
                            <input type="text" class="form-control" id="term_campaign" name="term_campaign"
                                   value="<?php echo htmlspecialchars($settings['term_campaign'] ?? $defaults['term_campaign']); ?>" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="term_groups" class="form-label">Groups (Plural)</label>
                            <input type="text" class="form-control" id="term_groups" name="term_groups"
                                   value="<?php echo htmlspecialchars($settings['term_groups'] ?? $defaults['term_groups']); ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="term_group" class="form-label">Group (Singular)</label>
                            <input type="text" class="form-control" id="term_group" name="term_group"
                                   value="<?php echo htmlspecialchars($settings['term_group'] ?? $defaults['term_group']); ?>" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="term_leaders" class="form-label">Leaders (Plural)</label>
                            <input type="text" class="form-control" id="term_leaders" name="term_leaders"
                                   value="<?php echo htmlspecialchars($settings['term_leaders'] ?? $defaults['term_leaders']); ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="term_leader" class="form-label">Leader (Singular)</label>
                            <input type="text" class="form-control" id="term_leader" name="term_leader"
                                   value="<?php echo htmlspecialchars($settings['term_leader'] ?? $defaults['term_leader']); ?>" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="term_volunteers" class="form-label">Volunteers (Plural)</label>
                            <input type="text" class="form-control" id="term_volunteers" name="term_volunteers"
                                   value="<?php echo htmlspecialchars($settings['term_volunteers'] ?? $defaults['term_volunteers']); ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="term_volunteer" class="form-label">Volunteer (Singular)</label>
                            <input type="text" class="form-control" id="term_volunteer" name="term_volunteer"
                                   value="<?php echo htmlspecialchars($settings['term_volunteer'] ?? $defaults['term_volunteer']); ?>" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="term_donations" class="form-label">Donations (Plural)</label>
                            <input type="text" class="form-control" id="term_donations" name="term_donations"
                                   value="<?php echo htmlspecialchars($settings['term_donations'] ?? $defaults['term_donations']); ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="term_donation" class="form-label">Donation (Singular)</label>
                            <input type="text" class="form-control" id="term_donation" name="term_donation"
                                   value="<?php echo htmlspecialchars($settings['term_donation'] ?? $defaults['term_donation']); ?>" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="term_attendance" class="form-label">Attendance</label>
                            <input type="text" class="form-control" id="term_attendance" name="term_attendance"
                                   value="<?php echo htmlspecialchars($settings['term_attendance'] ?? $defaults['term_attendance']); ?>" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="term_ministries" class="form-label">Ministries (Plural)</label>
                            <input type="text" class="form-control" id="term_ministries" name="term_ministries"
                                   value="<?php echo htmlspecialchars($settings['term_ministries'] ?? $defaults['term_ministries']); ?>" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="term_ministry" class="form-label">Ministry (Singular)</label>
                            <input type="text" class="form-control" id="term_ministry" name="term_ministry"
                                   value="<?php echo htmlspecialchars($settings['term_ministry'] ?? $defaults['term_ministry']); ?>" required>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> Save Terminology Settings
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- White-label Tab -->
    <div class="tab-pane fade" id="whitelabel" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-shield-check"></i> White-label Configuration
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Advanced Settings:</strong> These settings affect the overall branding and may require technical knowledge.
                </div>

                <form method="POST">
                    <input type="hidden" name="action" value="update_whitelabel">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="custom_domain" class="form-label">Custom Domain</label>
                            <input type="text" class="form-control" id="custom_domain" name="custom_domain"
                                   value="<?php echo htmlspecialchars($settings['custom_domain'] ?? ''); ?>"
                                   placeholder="yourdomain.com">
                            <div class="form-text">Domain for white-label deployment</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" id="ssl_enabled" name="ssl_enabled"
                                       <?php echo ($settings['ssl_enabled'] ?? 0) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="ssl_enabled">
                                    SSL/HTTPS Enabled
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="support_email" class="form-label">Support Email</label>
                            <input type="email" class="form-control" id="support_email" name="support_email"
                                   value="<?php echo htmlspecialchars($settings['support_email'] ?? ''); ?>"
                                   placeholder="<EMAIL>">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="support_phone" class="form-label">Support Phone</label>
                            <input type="tel" class="form-control" id="support_phone" name="support_phone"
                                   value="<?php echo htmlspecialchars($settings['support_phone'] ?? ''); ?>"
                                   placeholder="+****************">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="support_url" class="form-label">Support URL</label>
                            <input type="url" class="form-control" id="support_url" name="support_url"
                                   value="<?php echo htmlspecialchars($settings['support_url'] ?? ''); ?>"
                                   placeholder="https://support.yourdomain.com">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="custom_footer_text" class="form-label">Custom Footer Text</label>
                        <textarea class="form-control" id="custom_footer_text" name="custom_footer_text" rows="2"
                                  placeholder="© 2024 Your Organization. All rights reserved."><?php echo htmlspecialchars($settings['custom_footer_text'] ?? ''); ?></textarea>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="hide_powered_by" name="hide_powered_by"
                                   <?php echo ($settings['hide_powered_by'] ?? 0) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="hide_powered_by">
                                Hide "Powered by" branding
                            </label>
                        </div>
                    </div>

                    <hr class="my-4">

                    <h6 class="mb-3">
                        <i class="bi bi-graph-up"></i> Analytics & Tracking
                    </h6>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="google_analytics_id" class="form-label">Google Analytics ID</label>
                            <input type="text" class="form-control" id="google_analytics_id" name="google_analytics_id"
                                   value="<?php echo htmlspecialchars($settings['google_analytics_id'] ?? ''); ?>"
                                   placeholder="GA-XXXXXXXXX-X">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="facebook_pixel_id" class="form-label">Facebook Pixel ID</label>
                            <input type="text" class="form-control" id="facebook_pixel_id" name="facebook_pixel_id"
                                   value="<?php echo htmlspecialchars($settings['facebook_pixel_id'] ?? ''); ?>"
                                   placeholder="123456789012345">
                        </div>
                    </div>

                    <hr class="my-4">

                    <h6 class="mb-3">
                        <i class="bi bi-code-slash"></i> Custom Code Injection
                    </h6>

                    <div class="mb-3">
                        <label for="custom_head_code" class="form-label">Custom Head Code</label>
                        <textarea class="form-control" id="custom_head_code" name="custom_head_code" rows="4"
                                  placeholder="<!-- Custom code to be inserted in <head> section -->"><?php echo htmlspecialchars($settings['custom_head_code'] ?? ''); ?></textarea>
                        <div class="form-text">Code inserted before closing &lt;/head&gt; tag</div>
                    </div>

                    <div class="mb-3">
                        <label for="custom_body_code" class="form-label">Custom Body Code</label>
                        <textarea class="form-control" id="custom_body_code" name="custom_body_code" rows="4"
                                  placeholder="<!-- Custom code to be inserted before closing </body> tag -->"><?php echo htmlspecialchars($settings['custom_body_code'] ?? ''); ?></textarea>
                        <div class="form-text">Code inserted before closing &lt;/body&gt; tag</div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> Save White-label Settings
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Color picker synchronization
document.querySelectorAll('input[type="color"]').forEach(function(colorInput) {
    const textInput = colorInput.nextElementSibling;

    colorInput.addEventListener('change', function() {
        textInput.value = this.value;
    });

    textInput.addEventListener('change', function() {
        if (/^#[0-9A-F]{6}$/i.test(this.value)) {
            colorInput.value = this.value;
        }
    });
});

// Preview branding function
function previewBranding() {
    const organizationName = document.getElementById('organization_name').value;
    const primaryColor = document.getElementById('primary_color').value;
    const secondaryColor = document.getElementById('secondary_color').value;

    // Create preview window
    const previewWindow = window.open('', 'brandingPreview', 'width=800,height=600,scrollbars=yes');
    previewWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Branding Preview - ${organizationName}</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <style>
                :root {
                    --primary-color: ${primaryColor};
                    --secondary-color: ${secondaryColor};
                }
                .navbar { background-color: var(--primary-color) !important; }
                .btn-primary { background-color: var(--primary-color); border-color: var(--primary-color); }
                .text-primary { color: var(--primary-color) !important; }
                .bg-secondary { background-color: var(--secondary-color) !important; }
            </style>
        </head>
        <body>
            <nav class="navbar navbar-dark">
                <div class="container">
                    <span class="navbar-brand">${organizationName}</span>
                </div>
            </nav>
            <div class="container mt-4">
                <h1 class="text-primary">Welcome to ${organizationName}</h1>
                <p class="lead">This is a preview of how your branding will look.</p>
                <button class="btn btn-primary">Primary Button</button>
                <button class="btn btn-secondary ms-2">Secondary Button</button>
                <div class="mt-4 p-3 bg-light">
                    <h5>Sample Content</h5>
                    <p>This preview shows how your custom colors and branding will appear throughout the system.</p>
                </div>
            </div>
        </body>
        </html>
    `);
    previewWindow.document.close();
}

// Auto-sync terminology fields
document.getElementById('term_members').addEventListener('input', function() {
    const singular = this.value.replace(/s$/, '');
    if (!document.getElementById('term_member').value || document.getElementById('term_member').value === this.value.replace(/s$/, '')) {
        document.getElementById('term_member').value = singular;
    }
});

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>

<?php include 'includes/footer.php'; ?>
