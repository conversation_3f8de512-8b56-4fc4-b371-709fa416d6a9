<?php
/**
 * Database Verification Test
 * Final verification that the database is working correctly with the application
 */

require_once 'campaign/church/config.php';

echo "<h1>Database Verification Test</h1>\n";
echo "<p>Testing database functionality after synchronization...</p>\n";

$testResults = [];
$allTestsPassed = true;

// Test 1: Basic Connection
try {
    $pdo->query("SELECT 1");
    $testResults['connection'] = ['status' => 'PASS', 'message' => 'Database connection successful'];
    echo "<p style='color: green;'>✓ Test 1: Database Connection - PASSED</p>\n";
} catch (Exception $e) {
    $testResults['connection'] = ['status' => 'FAIL', 'message' => $e->getMessage()];
    echo "<p style='color: red;'>✗ Test 1: Database Connection - FAILED: " . $e->getMessage() . "</p>\n";
    $allTestsPassed = false;
}

// Test 2: Members Table Operations
try {
    // Test SELECT
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM members");
    $memberCount = $stmt->fetch()['count'];
    
    // Test INSERT (with rollback)
    $pdo->beginTransaction();
    $stmt = $pdo->prepare("INSERT INTO members (full_name, email, created_at) VALUES (?, ?, NOW())");
    $stmt->execute(['Test User', '<EMAIL>']);
    $insertId = $pdo->lastInsertId();
    
    // Test UPDATE
    $stmt = $pdo->prepare("UPDATE members SET full_name = ? WHERE id = ?");
    $stmt->execute(['Test User Updated', $insertId]);
    
    // Test DELETE
    $stmt = $pdo->prepare("DELETE FROM members WHERE id = ?");
    $stmt->execute([$insertId]);
    
    $pdo->rollback(); // Rollback test data
    
    $testResults['members_crud'] = ['status' => 'PASS', 'message' => "CRUD operations successful. Member count: $memberCount"];
    echo "<p style='color: green;'>✓ Test 2: Members CRUD Operations - PASSED ($memberCount members)</p>\n";
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollback();
    }
    $testResults['members_crud'] = ['status' => 'FAIL', 'message' => $e->getMessage()];
    echo "<p style='color: red;'>✗ Test 2: Members CRUD Operations - FAILED: " . $e->getMessage() . "</p>\n";
    $allTestsPassed = false;
}

// Test 3: Email Templates and Logs
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM email_templates");
    $templateCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM email_logs");
    $logCount = $stmt->fetch()['count'];
    
    // Test foreign key relationship
    $stmt = $pdo->query("
        SELECT COUNT(*) as count 
        FROM email_logs el 
        JOIN members m ON el.member_id = m.id 
        JOIN email_templates et ON el.template_id = et.id
    ");
    $joinCount = $stmt->fetch()['count'];
    
    $testResults['email_system'] = ['status' => 'PASS', 'message' => "Templates: $templateCount, Logs: $logCount, Valid joins: $joinCount"];
    echo "<p style='color: green;'>✓ Test 3: Email System - PASSED (Templates: $templateCount, Logs: $logCount)</p>\n";
} catch (Exception $e) {
    $testResults['email_system'] = ['status' => 'FAIL', 'message' => $e->getMessage()];
    echo "<p style='color: red;'>✗ Test 3: Email System - FAILED: " . $e->getMessage() . "</p>\n";
    $allTestsPassed = false;
}

// Test 4: Contact Management
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM contacts");
    $contactCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM contact_groups");
    $groupCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM contact_email_logs");
    $contactLogCount = $stmt->fetch()['count'];
    
    $testResults['contact_system'] = ['status' => 'PASS', 'message' => "Contacts: $contactCount, Groups: $groupCount, Logs: $contactLogCount"];
    echo "<p style='color: green;'>✓ Test 4: Contact Management - PASSED (Contacts: $contactCount, Groups: $groupCount)</p>\n";
} catch (Exception $e) {
    $testResults['contact_system'] = ['status' => 'FAIL', 'message' => $e->getMessage()];
    echo "<p style='color: red;'>✗ Test 4: Contact Management - FAILED: " . $e->getMessage() . "</p>\n";
    $allTestsPassed = false;
}

// Test 5: Email Tracking
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM email_tracking");
    $trackingCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("
        SELECT COUNT(*) as opened_count 
        FROM email_tracking 
        WHERE opened_at IS NOT NULL
    ");
    $openedCount = $stmt->fetch()['opened_count'];
    
    $openRate = $trackingCount > 0 ? round(($openedCount / $trackingCount) * 100, 2) : 0;
    
    $testResults['email_tracking'] = ['status' => 'PASS', 'message' => "Tracking records: $trackingCount, Opened: $openedCount, Rate: $openRate%"];
    echo "<p style='color: green;'>✓ Test 5: Email Tracking - PASSED (Records: $trackingCount, Open rate: $openRate%)</p>\n";
} catch (Exception $e) {
    $testResults['email_tracking'] = ['status' => 'FAIL', 'message' => $e->getMessage()];
    echo "<p style='color: red;'>✗ Test 5: Email Tracking - FAILED: " . $e->getMessage() . "</p>\n";
    $allTestsPassed = false;
}

// Test 6: Settings and Configuration
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM settings");
    $settingsCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM email_settings");
    $emailSettingsCount = $stmt->fetch()['count'];
    
    $testResults['settings'] = ['status' => 'PASS', 'message' => "Settings: $settingsCount, Email settings: $emailSettingsCount"];
    echo "<p style='color: green;'>✓ Test 6: Settings System - PASSED (Settings: $settingsCount, Email: $emailSettingsCount)</p>\n";
} catch (Exception $e) {
    $testResults['settings'] = ['status' => 'FAIL', 'message' => $e->getMessage()];
    echo "<p style='color: red;'>✗ Test 6: Settings System - FAILED: " . $e->getMessage() . "</p>\n";
    $allTestsPassed = false;
}

// Test 7: Performance Check
try {
    $startTime = microtime(true);
    
    // Run a complex query to test performance
    $stmt = $pdo->query("
        SELECT 
            m.id,
            m.full_name,
            m.email,
            COUNT(el.id) as email_count,
            COUNT(et.id) as tracking_count
        FROM members m
        LEFT JOIN email_logs el ON m.id = el.member_id
        LEFT JOIN email_tracking et ON m.id = et.member_id
        GROUP BY m.id, m.full_name, m.email
        LIMIT 10
    ");
    
    $results = $stmt->fetchAll();
    $endTime = microtime(true);
    $queryTime = round(($endTime - $startTime) * 1000, 2);
    
    $testResults['performance'] = ['status' => 'PASS', 'message' => "Complex query executed in {$queryTime}ms"];
    echo "<p style='color: green;'>✓ Test 7: Performance Check - PASSED (Query time: {$queryTime}ms)</p>\n";
} catch (Exception $e) {
    $testResults['performance'] = ['status' => 'FAIL', 'message' => $e->getMessage()];
    echo "<p style='color: red;'>✗ Test 7: Performance Check - FAILED: " . $e->getMessage() . "</p>\n";
    $allTestsPassed = false;
}

// Final Summary
echo "<h2>Verification Summary</h2>\n";

if ($allTestsPassed) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h3 style='color: #155724; margin-top: 0;'>🎉 All Tests Passed!</h3>\n";
    echo "<p style='color: #155724; font-size: 16px;'><strong>Your database is fully synchronized and working correctly with the codebase.</strong></p>\n";
    echo "<p style='color: #155724;'>The application should now function properly with all database operations.</p>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h3 style='color: #721c24; margin-top: 0;'>⚠ Some Tests Failed</h3>\n";
    echo "<p style='color: #721c24;'>Please review the failed tests above and address any issues.</p>\n";
    echo "</div>\n";
}

echo "<h3>Detailed Test Results</h3>\n";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
echo "<tr style='background: #f8f9fa;'><th>Test</th><th>Status</th><th>Details</th></tr>\n";

foreach ($testResults as $testName => $result) {
    $statusColor = $result['status'] === 'PASS' ? 'green' : 'red';
    $statusIcon = $result['status'] === 'PASS' ? '✓' : '✗';
    
    echo "<tr>\n";
    echo "<td>" . ucwords(str_replace('_', ' ', $testName)) . "</td>\n";
    echo "<td style='color: $statusColor; font-weight: bold;'>$statusIcon {$result['status']}</td>\n";
    echo "<td>{$result['message']}</td>\n";
    echo "</tr>\n";
}

echo "</table>\n";

echo "<h3>Next Steps</h3>\n";
echo "<ol>\n";
echo "<li><strong>Test Application Functionality:</strong> Navigate to your application and test key features</li>\n";
echo "<li><strong>Monitor Performance:</strong> Watch for any slow queries or performance issues</li>\n";
echo "<li><strong>Backup Schedule:</strong> Set up regular database backups</li>\n";
echo "<li><strong>Security Review:</strong> Ensure all security settings are properly configured</li>\n";
echo "</ol>\n";

echo "<p><em>Database synchronization completed on " . date('Y-m-d H:i:s') . "</em></p>\n";

?>
