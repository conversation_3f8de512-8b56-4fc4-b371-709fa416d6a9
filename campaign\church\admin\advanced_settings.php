<?php
session_start();

require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $pdo->beginTransaction();
        
        // Contact Information
        $contactSettings = [
            'contact_phone' => $_POST['contact_phone'] ?? '',
            'contact_email' => $_POST['contact_email'] ?? '',
            'contact_address' => $_POST['contact_address'] ?? '',
            'contact_city' => $_POST['contact_city'] ?? '',
            'contact_state' => $_POST['contact_state'] ?? '',
            'contact_zip' => $_POST['contact_zip'] ?? '',
            'contact_country' => $_POST['contact_country'] ?? '',
            'office_hours' => $_POST['office_hours'] ?? '',
            'emergency_contact' => $_POST['emergency_contact'] ?? ''
        ];
        
        // Social Media Links
        $socialSettings = [
            'facebook_url' => $_POST['facebook_url'] ?? '',
            'twitter_url' => $_POST['twitter_url'] ?? '',
            'instagram_url' => $_POST['instagram_url'] ?? '',
            'youtube_url' => $_POST['youtube_url'] ?? '',
            'linkedin_url' => $_POST['linkedin_url'] ?? '',
            'tiktok_url' => $_POST['tiktok_url'] ?? '',
            'website_url' => $_POST['website_url'] ?? '',
            'blog_url' => $_POST['blog_url'] ?? ''
        ];
        
        // System Preferences
        $systemSettings = [
            'timezone' => $_POST['timezone'] ?? 'America/New_York',
            'date_format' => $_POST['date_format'] ?? 'Y-m-d',
            'time_format' => $_POST['time_format'] ?? 'H:i',
            'currency_symbol' => $_POST['currency_symbol'] ?? '$',
            'currency_code' => $_POST['currency_code'] ?? 'USD',
            'language' => $_POST['language'] ?? 'en',
            'items_per_page' => $_POST['items_per_page'] ?? '25',
            'session_timeout' => $_POST['session_timeout'] ?? '3600',
            'max_upload_size' => $_POST['max_upload_size'] ?? '10',
            'backup_retention_days' => $_POST['backup_retention_days'] ?? '30'
        ];
        
        // Security Settings
        $securitySettings = [
            'enable_2fa' => isset($_POST['enable_2fa']) ? '1' : '0',
            'password_min_length' => $_POST['password_min_length'] ?? '8',
            'password_require_uppercase' => isset($_POST['password_require_uppercase']) ? '1' : '0',
            'password_require_lowercase' => isset($_POST['password_require_lowercase']) ? '1' : '0',
            'password_require_numbers' => isset($_POST['password_require_numbers']) ? '1' : '0',
            'password_require_symbols' => isset($_POST['password_require_symbols']) ? '1' : '0',
            'login_attempts_limit' => $_POST['login_attempts_limit'] ?? '5',
            'lockout_duration' => $_POST['lockout_duration'] ?? '900',
            'enable_login_notifications' => isset($_POST['enable_login_notifications']) ? '1' : '0',
            'enable_audit_log' => isset($_POST['enable_audit_log']) ? '1' : '0'
        ];
        
        // Email Configuration
        $emailSettings = [
            'smtp_host' => $_POST['smtp_host'] ?? '',
            'smtp_port' => $_POST['smtp_port'] ?? '587',
            'smtp_username' => $_POST['smtp_username'] ?? '',
            'smtp_password' => $_POST['smtp_password'] ?? '',
            'smtp_encryption' => $_POST['smtp_encryption'] ?? 'tls',
            'from_email' => $_POST['from_email'] ?? '',
            'from_name' => $_POST['from_name'] ?? '',
            'reply_to_email' => $_POST['reply_to_email'] ?? '',
            'email_signature' => $_POST['email_signature'] ?? '',
            'enable_email_queue' => isset($_POST['enable_email_queue']) ? '1' : '0'
        ];
        
        // Notification Settings
        $notificationSettings = [
            'enable_birthday_notifications' => isset($_POST['enable_birthday_notifications']) ? '1' : '0',
            'birthday_notification_days' => $_POST['birthday_notification_days'] ?? '7',
            'enable_event_reminders' => isset($_POST['enable_event_reminders']) ? '1' : '0',
            'event_reminder_days' => $_POST['event_reminder_days'] ?? '3',
            'enable_membership_expiry_alerts' => isset($_POST['enable_membership_expiry_alerts']) ? '1' : '0',
            'membership_expiry_days' => $_POST['membership_expiry_days'] ?? '30',
            'enable_admin_notifications' => isset($_POST['enable_admin_notifications']) ? '1' : '0',
            'admin_notification_email' => $_POST['admin_notification_email'] ?? '',
            'notification_frequency' => $_POST['notification_frequency'] ?? 'daily'
        ];
        
        // Integration Settings
        $integrationSettings = [
            'google_analytics_id' => $_POST['google_analytics_id'] ?? '',
            'facebook_pixel_id' => $_POST['facebook_pixel_id'] ?? '',
            'google_maps_api_key' => $_POST['google_maps_api_key'] ?? '',
            'whatsapp_api_token' => $_POST['whatsapp_api_token'] ?? '',
            'sms_api_key' => $_POST['sms_api_key'] ?? '',
            'payment_gateway' => $_POST['payment_gateway'] ?? 'stripe',
            'stripe_public_key' => $_POST['stripe_public_key'] ?? '',
            'stripe_secret_key' => $_POST['stripe_secret_key'] ?? '',
            'paypal_client_id' => $_POST['paypal_client_id'] ?? '',
            'enable_api_access' => isset($_POST['enable_api_access']) ? '1' : '0'
        ];
        
        // Save all settings
        $allSettings = array_merge(
            $contactSettings, 
            $socialSettings, 
            $systemSettings, 
            $securitySettings, 
            $emailSettings, 
            $notificationSettings, 
            $integrationSettings
        );
        
        foreach ($allSettings as $key => $value) {
            update_site_setting($key, $value);
        }
        
        $pdo->commit();
        $success_message = "Advanced settings updated successfully!";
        
    } catch (Exception $e) {
        $pdo->rollback();
        $error_message = "Error updating settings: " . $e->getMessage();
    }
}

// Get current settings
$currentSettings = [];
$settingKeys = [
    // Contact
    'contact_phone', 'contact_email', 'contact_address', 'contact_city', 'contact_state', 
    'contact_zip', 'contact_country', 'office_hours', 'emergency_contact',
    // Social Media
    'facebook_url', 'twitter_url', 'instagram_url', 'youtube_url', 'linkedin_url', 
    'tiktok_url', 'website_url', 'blog_url',
    // System
    'timezone', 'date_format', 'time_format', 'currency_symbol', 'currency_code', 
    'language', 'items_per_page', 'session_timeout', 'max_upload_size', 'backup_retention_days',
    // Security
    'enable_2fa', 'password_min_length', 'password_require_uppercase', 'password_require_lowercase',
    'password_require_numbers', 'password_require_symbols', 'login_attempts_limit', 'lockout_duration',
    'enable_login_notifications', 'enable_audit_log',
    // Email
    'smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_encryption',
    'from_email', 'from_name', 'reply_to_email', 'email_signature', 'enable_email_queue',
    // Notifications
    'enable_birthday_notifications', 'birthday_notification_days', 'enable_event_reminders',
    'event_reminder_days', 'enable_membership_expiry_alerts', 'membership_expiry_days',
    'enable_admin_notifications', 'admin_notification_email', 'notification_frequency',
    // Integrations
    'google_analytics_id', 'facebook_pixel_id', 'google_maps_api_key', 'whatsapp_api_token',
    'sms_api_key', 'payment_gateway', 'stripe_public_key', 'stripe_secret_key',
    'paypal_client_id', 'enable_api_access'
];

foreach ($settingKeys as $key) {
    $currentSettings[$key] = get_site_setting($key, '');
}

$page_title = "Advanced Settings";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="bi bi-gear-fill"></i> Advanced Settings
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-outline-secondary" id="export-settings">
                        <i class="bi bi-download"></i> Export Settings
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-2" id="import-settings">
                        <i class="bi bi-upload"></i> Import Settings
                    </button>
                </div>
            </div>

            <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Settings Navigation Tabs -->
            <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab">
                        <i class="bi bi-telephone"></i> Contact
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="social-tab" data-bs-toggle="tab" data-bs-target="#social" type="button" role="tab">
                        <i class="bi bi-share"></i> Social Media
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                        <i class="bi bi-cpu"></i> System
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
                        <i class="bi bi-shield-check"></i> Security
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="email-tab" data-bs-toggle="tab" data-bs-target="#email" type="button" role="tab">
                        <i class="bi bi-envelope"></i> Email
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="notifications-tab" data-bs-toggle="tab" data-bs-target="#notifications" type="button" role="tab">
                        <i class="bi bi-bell"></i> Notifications
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="integrations-tab" data-bs-toggle="tab" data-bs-target="#integrations" type="button" role="tab">
                        <i class="bi bi-puzzle"></i> Integrations
                    </button>
                </li>
            </ul>

            <form method="POST" id="advanced-settings-form">
                <div class="tab-content" id="settingsTabContent">
                    <!-- Contact Information Tab -->
                    <div class="tab-pane fade show active" id="contact" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">Contact Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-lg-6 mb-3">
                                        <label for="contact_phone" class="form-label">Phone Number</label>
                                        <input type="tel" class="form-control" id="contact_phone" name="contact_phone" value="<?php echo htmlspecialchars($currentSettings['contact_phone']); ?>">
                                    </div>
                                    <div class="col-lg-6 mb-3">
                                        <label for="contact_email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="contact_email" name="contact_email" value="<?php echo htmlspecialchars($currentSettings['contact_email']); ?>">
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="contact_address" class="form-label">Street Address</label>
                                    <input type="text" class="form-control" id="contact_address" name="contact_address" value="<?php echo htmlspecialchars($currentSettings['contact_address']); ?>">
                                </div>
                                
                                <div class="row">
                                    <div class="col-lg-4 mb-3">
                                        <label for="contact_city" class="form-label">City</label>
                                        <input type="text" class="form-control" id="contact_city" name="contact_city" value="<?php echo htmlspecialchars($currentSettings['contact_city']); ?>">
                                    </div>
                                    <div class="col-lg-4 mb-3">
                                        <label for="contact_state" class="form-label">State/Province</label>
                                        <input type="text" class="form-control" id="contact_state" name="contact_state" value="<?php echo htmlspecialchars($currentSettings['contact_state']); ?>">
                                    </div>
                                    <div class="col-lg-4 mb-3">
                                        <label for="contact_zip" class="form-label">ZIP/Postal Code</label>
                                        <input type="text" class="form-control" id="contact_zip" name="contact_zip" value="<?php echo htmlspecialchars($currentSettings['contact_zip']); ?>">
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-lg-6 mb-3">
                                        <label for="contact_country" class="form-label">Country</label>
                                        <input type="text" class="form-control" id="contact_country" name="contact_country" value="<?php echo htmlspecialchars($currentSettings['contact_country']); ?>">
                                    </div>
                                    <div class="col-lg-6 mb-3">
                                        <label for="emergency_contact" class="form-label">Emergency Contact</label>
                                        <input type="text" class="form-control" id="emergency_contact" name="emergency_contact" value="<?php echo htmlspecialchars($currentSettings['emergency_contact']); ?>">
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="office_hours" class="form-label">Office Hours</label>
                                    <textarea class="form-control" id="office_hours" name="office_hours" rows="3" placeholder="Monday - Friday: 9:00 AM - 5:00 PM&#10;Saturday: 10:00 AM - 2:00 PM&#10;Sunday: Closed"><?php echo htmlspecialchars($currentSettings['office_hours']); ?></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Social Media Tab -->
                    <div class="tab-pane fade" id="social" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">Social Media Links</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-lg-6 mb-3">
                                        <label for="facebook_url" class="form-label">
                                            <i class="bi bi-facebook text-primary"></i> Facebook URL
                                        </label>
                                        <input type="url" class="form-control" id="facebook_url" name="facebook_url" value="<?php echo htmlspecialchars($currentSettings['facebook_url']); ?>" placeholder="https://facebook.com/yourpage">
                                    </div>
                                    <div class="col-lg-6 mb-3">
                                        <label for="twitter_url" class="form-label">
                                            <i class="bi bi-twitter text-info"></i> Twitter URL
                                        </label>
                                        <input type="url" class="form-control" id="twitter_url" name="twitter_url" value="<?php echo htmlspecialchars($currentSettings['twitter_url']); ?>" placeholder="https://twitter.com/youraccount">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-6 mb-3">
                                        <label for="instagram_url" class="form-label">
                                            <i class="bi bi-instagram text-danger"></i> Instagram URL
                                        </label>
                                        <input type="url" class="form-control" id="instagram_url" name="instagram_url" value="<?php echo htmlspecialchars($currentSettings['instagram_url']); ?>" placeholder="https://instagram.com/youraccount">
                                    </div>
                                    <div class="col-lg-6 mb-3">
                                        <label for="youtube_url" class="form-label">
                                            <i class="bi bi-youtube text-danger"></i> YouTube URL
                                        </label>
                                        <input type="url" class="form-control" id="youtube_url" name="youtube_url" value="<?php echo htmlspecialchars($currentSettings['youtube_url']); ?>" placeholder="https://youtube.com/yourchannel">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-6 mb-3">
                                        <label for="linkedin_url" class="form-label">
                                            <i class="bi bi-linkedin text-primary"></i> LinkedIn URL
                                        </label>
                                        <input type="url" class="form-control" id="linkedin_url" name="linkedin_url" value="<?php echo htmlspecialchars($currentSettings['linkedin_url']); ?>" placeholder="https://linkedin.com/company/yourcompany">
                                    </div>
                                    <div class="col-lg-6 mb-3">
                                        <label for="tiktok_url" class="form-label">
                                            <i class="bi bi-tiktok text-dark"></i> TikTok URL
                                        </label>
                                        <input type="url" class="form-control" id="tiktok_url" name="tiktok_url" value="<?php echo htmlspecialchars($currentSettings['tiktok_url']); ?>" placeholder="https://tiktok.com/@youraccount">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-6 mb-3">
                                        <label for="website_url" class="form-label">
                                            <i class="bi bi-globe text-success"></i> Website URL
                                        </label>
                                        <input type="url" class="form-control" id="website_url" name="website_url" value="<?php echo htmlspecialchars($currentSettings['website_url']); ?>" placeholder="https://yourwebsite.com">
                                    </div>
                                    <div class="col-lg-6 mb-3">
                                        <label for="blog_url" class="form-label">
                                            <i class="bi bi-journal-text text-secondary"></i> Blog URL
                                        </label>
                                        <input type="url" class="form-control" id="blog_url" name="blog_url" value="<?php echo htmlspecialchars($currentSettings['blog_url']); ?>" placeholder="https://yourblog.com">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- System Settings Tab -->
                    <div class="tab-pane fade" id="system" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">System Preferences</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-lg-6 mb-3">
                                        <label for="timezone" class="form-label">Timezone</label>
                                        <select class="form-select" id="timezone" name="timezone">
                                            <?php
                                            $timezones = [
                                                'America/New_York' => 'Eastern Time (ET)',
                                                'America/Chicago' => 'Central Time (CT)',
                                                'America/Denver' => 'Mountain Time (MT)',
                                                'America/Los_Angeles' => 'Pacific Time (PT)',
                                                'America/Phoenix' => 'Arizona Time',
                                                'America/Anchorage' => 'Alaska Time',
                                                'Pacific/Honolulu' => 'Hawaii Time',
                                                'UTC' => 'UTC',
                                                'Europe/London' => 'London Time',
                                                'Europe/Paris' => 'Central European Time',
                                                'Asia/Tokyo' => 'Japan Time',
                                                'Australia/Sydney' => 'Australian Eastern Time'
                                            ];
                                            foreach ($timezones as $value => $label):
                                            ?>
                                                <option value="<?php echo $value; ?>" <?php echo ($currentSettings['timezone'] === $value) ? 'selected' : ''; ?>><?php echo $label; ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-lg-6 mb-3">
                                        <label for="language" class="form-label">Language</label>
                                        <select class="form-select" id="language" name="language">
                                            <option value="en" <?php echo ($currentSettings['language'] === 'en') ? 'selected' : ''; ?>>English</option>
                                            <option value="es" <?php echo ($currentSettings['language'] === 'es') ? 'selected' : ''; ?>>Spanish</option>
                                            <option value="fr" <?php echo ($currentSettings['language'] === 'fr') ? 'selected' : ''; ?>>French</option>
                                            <option value="de" <?php echo ($currentSettings['language'] === 'de') ? 'selected' : ''; ?>>German</option>
                                            <option value="pt" <?php echo ($currentSettings['language'] === 'pt') ? 'selected' : ''; ?>>Portuguese</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-4 mb-3">
                                        <label for="date_format" class="form-label">Date Format</label>
                                        <select class="form-select" id="date_format" name="date_format">
                                            <option value="Y-m-d" <?php echo ($currentSettings['date_format'] === 'Y-m-d') ? 'selected' : ''; ?>>YYYY-MM-DD</option>
                                            <option value="m/d/Y" <?php echo ($currentSettings['date_format'] === 'm/d/Y') ? 'selected' : ''; ?>>MM/DD/YYYY</option>
                                            <option value="d/m/Y" <?php echo ($currentSettings['date_format'] === 'd/m/Y') ? 'selected' : ''; ?>>DD/MM/YYYY</option>
                                            <option value="M j, Y" <?php echo ($currentSettings['date_format'] === 'M j, Y') ? 'selected' : ''; ?>>Mon DD, YYYY</option>
                                        </select>
                                    </div>
                                    <div class="col-lg-4 mb-3">
                                        <label for="time_format" class="form-label">Time Format</label>
                                        <select class="form-select" id="time_format" name="time_format">
                                            <option value="H:i" <?php echo ($currentSettings['time_format'] === 'H:i') ? 'selected' : ''; ?>>24-hour (HH:MM)</option>
                                            <option value="g:i A" <?php echo ($currentSettings['time_format'] === 'g:i A') ? 'selected' : ''; ?>>12-hour (H:MM AM/PM)</option>
                                        </select>
                                    </div>
                                    <div class="col-lg-4 mb-3">
                                        <label for="items_per_page" class="form-label">Items Per Page</label>
                                        <select class="form-select" id="items_per_page" name="items_per_page">
                                            <option value="10" <?php echo ($currentSettings['items_per_page'] === '10') ? 'selected' : ''; ?>>10</option>
                                            <option value="25" <?php echo ($currentSettings['items_per_page'] === '25') ? 'selected' : ''; ?>>25</option>
                                            <option value="50" <?php echo ($currentSettings['items_per_page'] === '50') ? 'selected' : ''; ?>>50</option>
                                            <option value="100" <?php echo ($currentSettings['items_per_page'] === '100') ? 'selected' : ''; ?>>100</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-4 mb-3">
                                        <label for="currency_symbol" class="form-label">Currency Symbol</label>
                                        <input type="text" class="form-control" id="currency_symbol" name="currency_symbol" value="<?php echo htmlspecialchars($currentSettings['currency_symbol'] ?: '$'); ?>" maxlength="5">
                                    </div>
                                    <div class="col-lg-4 mb-3">
                                        <label for="currency_code" class="form-label">Currency Code</label>
                                        <input type="text" class="form-control" id="currency_code" name="currency_code" value="<?php echo htmlspecialchars($currentSettings['currency_code'] ?: 'USD'); ?>" maxlength="3">
                                    </div>
                                    <div class="col-lg-4 mb-3">
                                        <label for="session_timeout" class="form-label">Session Timeout (seconds)</label>
                                        <input type="number" class="form-control" id="session_timeout" name="session_timeout" value="<?php echo htmlspecialchars($currentSettings['session_timeout'] ?: '3600'); ?>" min="300" max="86400">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-6 mb-3">
                                        <label for="max_upload_size" class="form-label">Max Upload Size (MB)</label>
                                        <input type="number" class="form-control" id="max_upload_size" name="max_upload_size" value="<?php echo htmlspecialchars($currentSettings['max_upload_size'] ?: '10'); ?>" min="1" max="100">
                                    </div>
                                    <div class="col-lg-6 mb-3">
                                        <label for="backup_retention_days" class="form-label">Backup Retention (days)</label>
                                        <input type="number" class="form-control" id="backup_retention_days" name="backup_retention_days" value="<?php echo htmlspecialchars($currentSettings['backup_retention_days'] ?: '30'); ?>" min="7" max="365">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Security Settings Tab -->
                    <div class="tab-pane fade" id="security" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">Security Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-lg-6 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enable_2fa" name="enable_2fa" <?php echo ($currentSettings['enable_2fa'] === '1') ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="enable_2fa">
                                                Enable Two-Factor Authentication
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enable_audit_log" name="enable_audit_log" <?php echo ($currentSettings['enable_audit_log'] === '1') ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="enable_audit_log">
                                                Enable Audit Logging
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-4 mb-3">
                                        <label for="password_min_length" class="form-label">Minimum Password Length</label>
                                        <input type="number" class="form-control" id="password_min_length" name="password_min_length" value="<?php echo htmlspecialchars($currentSettings['password_min_length'] ?: '8'); ?>" min="6" max="20">
                                    </div>
                                    <div class="col-lg-4 mb-3">
                                        <label for="login_attempts_limit" class="form-label">Login Attempts Limit</label>
                                        <input type="number" class="form-control" id="login_attempts_limit" name="login_attempts_limit" value="<?php echo htmlspecialchars($currentSettings['login_attempts_limit'] ?: '5'); ?>" min="3" max="10">
                                    </div>
                                    <div class="col-lg-4 mb-3">
                                        <label for="lockout_duration" class="form-label">Lockout Duration (seconds)</label>
                                        <input type="number" class="form-control" id="lockout_duration" name="lockout_duration" value="<?php echo htmlspecialchars($currentSettings['lockout_duration'] ?: '900'); ?>" min="300" max="3600">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Password Requirements</label>
                                    <div class="row">
                                        <div class="col-lg-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="password_require_uppercase" name="password_require_uppercase" <?php echo ($currentSettings['password_require_uppercase'] === '1') ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="password_require_uppercase">
                                                    Uppercase Letters
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-lg-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="password_require_lowercase" name="password_require_lowercase" <?php echo ($currentSettings['password_require_lowercase'] === '1') ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="password_require_lowercase">
                                                    Lowercase Letters
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-lg-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="password_require_numbers" name="password_require_numbers" <?php echo ($currentSettings['password_require_numbers'] === '1') ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="password_require_numbers">
                                                    Numbers
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-lg-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="password_require_symbols" name="password_require_symbols" <?php echo ($currentSettings['password_require_symbols'] === '1') ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="password_require_symbols">
                                                    Special Characters
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-6 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enable_login_notifications" name="enable_login_notifications" <?php echo ($currentSettings['enable_login_notifications'] === '1') ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="enable_login_notifications">
                                                Email Login Notifications
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Email Configuration Tab -->
                    <div class="tab-pane fade" id="email" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">Email Configuration</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-lg-6 mb-3">
                                        <label for="smtp_host" class="form-label">SMTP Host</label>
                                        <input type="text" class="form-control" id="smtp_host" name="smtp_host" value="<?php echo htmlspecialchars($currentSettings['smtp_host']); ?>" placeholder="smtp.gmail.com">
                                    </div>
                                    <div class="col-lg-6 mb-3">
                                        <label for="smtp_port" class="form-label">SMTP Port</label>
                                        <input type="number" class="form-control" id="smtp_port" name="smtp_port" value="<?php echo htmlspecialchars($currentSettings['smtp_port'] ?: '587'); ?>" min="25" max="65535">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-6 mb-3">
                                        <label for="smtp_username" class="form-label">SMTP Username</label>
                                        <input type="text" class="form-control" id="smtp_username" name="smtp_username" value="<?php echo htmlspecialchars($currentSettings['smtp_username']); ?>">
                                    </div>
                                    <div class="col-lg-6 mb-3">
                                        <label for="smtp_password" class="form-label">SMTP Password</label>
                                        <input type="password" class="form-control" id="smtp_password" name="smtp_password" value="<?php echo htmlspecialchars($currentSettings['smtp_password']); ?>">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-4 mb-3">
                                        <label for="smtp_encryption" class="form-label">Encryption</label>
                                        <select class="form-select" id="smtp_encryption" name="smtp_encryption">
                                            <option value="tls" <?php echo ($currentSettings['smtp_encryption'] === 'tls') ? 'selected' : ''; ?>>TLS</option>
                                            <option value="ssl" <?php echo ($currentSettings['smtp_encryption'] === 'ssl') ? 'selected' : ''; ?>>SSL</option>
                                            <option value="none" <?php echo ($currentSettings['smtp_encryption'] === 'none') ? 'selected' : ''; ?>>None</option>
                                        </select>
                                    </div>
                                    <div class="col-lg-4 mb-3">
                                        <label for="from_email" class="form-label">From Email</label>
                                        <input type="email" class="form-control" id="from_email" name="from_email" value="<?php echo htmlspecialchars($currentSettings['from_email']); ?>">
                                    </div>
                                    <div class="col-lg-4 mb-3">
                                        <label for="from_name" class="form-label">From Name</label>
                                        <input type="text" class="form-control" id="from_name" name="from_name" value="<?php echo htmlspecialchars($currentSettings['from_name']); ?>">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-6 mb-3">
                                        <label for="reply_to_email" class="form-label">Reply-To Email</label>
                                        <input type="email" class="form-control" id="reply_to_email" name="reply_to_email" value="<?php echo htmlspecialchars($currentSettings['reply_to_email']); ?>">
                                    </div>
                                    <div class="col-lg-6 mb-3">
                                        <div class="form-check form-switch mt-4">
                                            <input class="form-check-input" type="checkbox" id="enable_email_queue" name="enable_email_queue" <?php echo ($currentSettings['enable_email_queue'] === '1') ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="enable_email_queue">
                                                Enable Email Queue
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="email_signature" class="form-label">Email Signature</label>
                                    <textarea class="form-control" id="email_signature" name="email_signature" rows="4" placeholder="Best regards,&#10;Your Organization Name"><?php echo htmlspecialchars($currentSettings['email_signature']); ?></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Notifications Tab -->
                    <div class="tab-pane fade" id="notifications" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">Notification Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-lg-6 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enable_birthday_notifications" name="enable_birthday_notifications" <?php echo ($currentSettings['enable_birthday_notifications'] === '1') ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="enable_birthday_notifications">
                                                Enable Birthday Notifications
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 mb-3">
                                        <label for="birthday_notification_days" class="form-label">Birthday Notification Days Ahead</label>
                                        <input type="number" class="form-control" id="birthday_notification_days" name="birthday_notification_days" value="<?php echo htmlspecialchars($currentSettings['birthday_notification_days'] ?: '7'); ?>" min="1" max="30">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-6 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enable_event_reminders" name="enable_event_reminders" <?php echo ($currentSettings['enable_event_reminders'] === '1') ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="enable_event_reminders">
                                                Enable Event Reminders
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 mb-3">
                                        <label for="event_reminder_days" class="form-label">Event Reminder Days Ahead</label>
                                        <input type="number" class="form-control" id="event_reminder_days" name="event_reminder_days" value="<?php echo htmlspecialchars($currentSettings['event_reminder_days'] ?: '3'); ?>" min="1" max="14">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-6 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enable_membership_expiry_alerts" name="enable_membership_expiry_alerts" <?php echo ($currentSettings['enable_membership_expiry_alerts'] === '1') ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="enable_membership_expiry_alerts">
                                                Enable Membership Expiry Alerts
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 mb-3">
                                        <label for="membership_expiry_days" class="form-label">Membership Expiry Alert Days</label>
                                        <input type="number" class="form-control" id="membership_expiry_days" name="membership_expiry_days" value="<?php echo htmlspecialchars($currentSettings['membership_expiry_days'] ?: '30'); ?>" min="7" max="90">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-6 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enable_admin_notifications" name="enable_admin_notifications" <?php echo ($currentSettings['enable_admin_notifications'] === '1') ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="enable_admin_notifications">
                                                Enable Admin Notifications
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 mb-3">
                                        <label for="admin_notification_email" class="form-label">Admin Notification Email</label>
                                        <input type="email" class="form-control" id="admin_notification_email" name="admin_notification_email" value="<?php echo htmlspecialchars($currentSettings['admin_notification_email']); ?>">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="notification_frequency" class="form-label">Notification Frequency</label>
                                    <select class="form-select" id="notification_frequency" name="notification_frequency">
                                        <option value="immediate" <?php echo ($currentSettings['notification_frequency'] === 'immediate') ? 'selected' : ''; ?>>Immediate</option>
                                        <option value="daily" <?php echo ($currentSettings['notification_frequency'] === 'daily') ? 'selected' : ''; ?>>Daily Digest</option>
                                        <option value="weekly" <?php echo ($currentSettings['notification_frequency'] === 'weekly') ? 'selected' : ''; ?>>Weekly Digest</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Integrations Tab -->
                    <div class="tab-pane fade" id="integrations" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">Third-Party Integrations</h5>
                            </div>
                            <div class="card-body">
                                <h6>Analytics & Tracking</h6>
                                <div class="row mb-4">
                                    <div class="col-lg-6 mb-3">
                                        <label for="google_analytics_id" class="form-label">Google Analytics ID</label>
                                        <input type="text" class="form-control" id="google_analytics_id" name="google_analytics_id" value="<?php echo htmlspecialchars($currentSettings['google_analytics_id']); ?>" placeholder="GA-XXXXXXXXX-X">
                                    </div>
                                    <div class="col-lg-6 mb-3">
                                        <label for="facebook_pixel_id" class="form-label">Facebook Pixel ID</label>
                                        <input type="text" class="form-control" id="facebook_pixel_id" name="facebook_pixel_id" value="<?php echo htmlspecialchars($currentSettings['facebook_pixel_id']); ?>" placeholder="123456789012345">
                                    </div>
                                </div>

                                <h6>Maps & Location</h6>
                                <div class="row mb-4">
                                    <div class="col-lg-12 mb-3">
                                        <label for="google_maps_api_key" class="form-label">Google Maps API Key</label>
                                        <input type="text" class="form-control" id="google_maps_api_key" name="google_maps_api_key" value="<?php echo htmlspecialchars($currentSettings['google_maps_api_key']); ?>">
                                    </div>
                                </div>

                                <h6>Communication</h6>
                                <div class="row mb-4">
                                    <div class="col-lg-6 mb-3">
                                        <label for="whatsapp_api_token" class="form-label">WhatsApp API Token</label>
                                        <input type="text" class="form-control" id="whatsapp_api_token" name="whatsapp_api_token" value="<?php echo htmlspecialchars($currentSettings['whatsapp_api_token']); ?>">
                                    </div>
                                    <div class="col-lg-6 mb-3">
                                        <label for="sms_api_key" class="form-label">SMS API Key</label>
                                        <input type="text" class="form-control" id="sms_api_key" name="sms_api_key" value="<?php echo htmlspecialchars($currentSettings['sms_api_key']); ?>">
                                    </div>
                                </div>

                                <h6>Payment Processing</h6>
                                <div class="row mb-4">
                                    <div class="col-lg-4 mb-3">
                                        <label for="payment_gateway" class="form-label">Payment Gateway</label>
                                        <select class="form-select" id="payment_gateway" name="payment_gateway">
                                            <option value="stripe" <?php echo ($currentSettings['payment_gateway'] === 'stripe') ? 'selected' : ''; ?>>Stripe</option>
                                            <option value="paypal" <?php echo ($currentSettings['payment_gateway'] === 'paypal') ? 'selected' : ''; ?>>PayPal</option>
                                            <option value="square" <?php echo ($currentSettings['payment_gateway'] === 'square') ? 'selected' : ''; ?>>Square</option>
                                        </select>
                                    </div>
                                    <div class="col-lg-4 mb-3">
                                        <label for="stripe_public_key" class="form-label">Stripe Public Key</label>
                                        <input type="text" class="form-control" id="stripe_public_key" name="stripe_public_key" value="<?php echo htmlspecialchars($currentSettings['stripe_public_key']); ?>">
                                    </div>
                                    <div class="col-lg-4 mb-3">
                                        <label for="stripe_secret_key" class="form-label">Stripe Secret Key</label>
                                        <input type="password" class="form-control" id="stripe_secret_key" name="stripe_secret_key" value="<?php echo htmlspecialchars($currentSettings['stripe_secret_key']); ?>">
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-lg-6 mb-3">
                                        <label for="paypal_client_id" class="form-label">PayPal Client ID</label>
                                        <input type="text" class="form-control" id="paypal_client_id" name="paypal_client_id" value="<?php echo htmlspecialchars($currentSettings['paypal_client_id']); ?>">
                                    </div>
                                    <div class="col-lg-6 mb-3">
                                        <div class="form-check form-switch mt-4">
                                            <input class="form-check-input" type="checkbox" id="enable_api_access" name="enable_api_access" <?php echo ($currentSettings['enable_api_access'] === '1') ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="enable_api_access">
                                                Enable API Access
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-save"></i> Save Advanced Settings
                        </button>
                        <button type="button" class="btn btn-outline-secondary ms-2" onclick="window.location.reload()">
                            <i class="bi bi-arrow-clockwise"></i> Cancel
                        </button>
                    </div>
                </div>
            </form>
        </main>
    </div>
</div>

<script>
// Settings export/import functionality
document.getElementById('export-settings').addEventListener('click', function() {
    // Create export data
    const formData = new FormData(document.getElementById('advanced-settings-form'));
    const settings = {};

    for (let [key, value] of formData.entries()) {
        settings[key] = value;
    }

    // Download as JSON
    const blob = new Blob([JSON.stringify(settings, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'advanced-settings-' + new Date().toISOString().split('T')[0] + '.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
});

document.getElementById('import-settings').addEventListener('click', function() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';

    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const settings = JSON.parse(e.target.result);

                    // Populate form with imported settings
                    Object.keys(settings).forEach(key => {
                        const element = document.querySelector(`[name="${key}"]`);
                        if (element) {
                            if (element.type === 'checkbox') {
                                element.checked = settings[key] === '1' || settings[key] === true;
                            } else {
                                element.value = settings[key];
                            }
                        }
                    });

                    alert('Settings imported successfully! Remember to save the form.');
                } catch (error) {
                    alert('Error importing settings: Invalid JSON file.');
                }
            };
            reader.readAsText(file);
        }
    };

    input.click();
});

// Tab persistence
const tabTriggerList = document.querySelectorAll('#settingsTabs button');
tabTriggerList.forEach(tabTrigger => {
    tabTrigger.addEventListener('click', function() {
        localStorage.setItem('activeSettingsTab', this.getAttribute('data-bs-target'));
    });
});

// Restore active tab
const activeTab = localStorage.getItem('activeSettingsTab');
if (activeTab) {
    const tabButton = document.querySelector(`[data-bs-target="${activeTab}"]`);
    if (tabButton) {
        const tab = new bootstrap.Tab(tabButton);
        tab.show();
    }
}
</script>

<?php include 'includes/footer.php'; ?>
