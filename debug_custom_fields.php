<?php
/**
 * Debug Custom Fields Page
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug Custom Fields Page</h1>\n";

try {
    echo "<p>1. Loading config...</p>\n";
    require_once 'campaign/church/config.php';
    echo "<p style='color: green;'>✓ Config loaded successfully</p>\n";
    
    echo "<p>2. Starting session...</p>\n";
    session_start();
    $_SESSION['admin_id'] = 4; // Set admin session for testing
    echo "<p style='color: green;'>✓ Session started, admin_id = 4</p>\n";
    
    echo "<p>3. Testing database connection...</p>\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM custom_field_definitions");
    $count = $stmt->fetch()['count'];
    echo "<p style='color: green;'>✓ Database connected, found $count custom field definitions</p>\n";
    
    echo "<p>4. Testing custom fields query...</p>\n";
    $entityFilter = 'member';
    $stmt = $pdo->prepare("
        SELECT cf.*, 
               COUNT(cfv.id) as usage_count,
               a.username as created_by_name
        FROM custom_field_definitions cf
        LEFT JOIN custom_field_values cfv ON cf.id = cfv.field_definition_id
        LEFT JOIN admins a ON cf.created_by = a.id
        WHERE cf.entity_type = ?
        GROUP BY cf.id
        ORDER BY cf.field_order ASC, cf.created_at DESC
    ");
    $stmt->execute([$entityFilter]);
    $customFields = $stmt->fetchAll();
    echo "<p style='color: green;'>✓ Custom fields query successful, found " . count($customFields) . " fields</p>\n";
    
    echo "<p>5. Testing includes...</p>\n";
    
    // Check if header file exists and can be included
    $headerPath = 'campaign/church/admin/includes/header.php';
    if (file_exists($headerPath)) {
        echo "<p style='color: green;'>✓ Header file exists: $headerPath</p>\n";
        
        // Try to capture header output
        ob_start();
        try {
            $page_title = "Custom Fields Management";
            include $headerPath;
            $headerOutput = ob_get_contents();
            ob_end_clean();
            echo "<p style='color: green;'>✓ Header included successfully</p>\n";
        } catch (Exception $e) {
            ob_end_clean();
            echo "<p style='color: red;'>✗ Header include error: " . $e->getMessage() . "</p>\n";
        }
    } else {
        echo "<p style='color: red;'>✗ Header file not found: $headerPath</p>\n";
    }
    
    echo "<p>6. Checking custom_fields.php file...</p>\n";
    $customFieldsPath = 'campaign/church/admin/custom_fields.php';
    if (file_exists($customFieldsPath)) {
        echo "<p style='color: green;'>✓ Custom fields file exists: $customFieldsPath</p>\n";
        
        // Get file size
        $fileSize = filesize($customFieldsPath);
        echo "<p>File size: " . number_format($fileSize) . " bytes</p>\n";
        
        // Check if file is readable
        if (is_readable($customFieldsPath)) {
            echo "<p style='color: green;'>✓ File is readable</p>\n";
        } else {
            echo "<p style='color: red;'>✗ File is not readable</p>\n";
        }
        
        // Try to get first few lines
        $handle = fopen($customFieldsPath, 'r');
        if ($handle) {
            $firstLine = fgets($handle);
            fclose($handle);
            echo "<p>First line: " . htmlspecialchars(trim($firstLine)) . "</p>\n";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Custom fields file not found: $customFieldsPath</p>\n";
    }
    
    echo "<p>7. Testing direct PHP execution...</p>\n";
    
    // Try to execute the custom fields page and capture any errors
    ob_start();
    $errorOutput = '';
    
    // Capture errors
    set_error_handler(function($severity, $message, $file, $line) use (&$errorOutput) {
        $errorOutput .= "Error: $message in $file on line $line\n";
    });
    
    try {
        // Include the custom fields page
        include $customFieldsPath;
        $pageOutput = ob_get_contents();
        ob_end_clean();
        
        if (!empty($errorOutput)) {
            echo "<p style='color: red;'>✗ PHP Errors found:</p>\n";
            echo "<pre style='background: #ffe6e6; padding: 10px; border: 1px solid #ff0000;'>" . htmlspecialchars($errorOutput) . "</pre>\n";
        } else {
            echo "<p style='color: green;'>✓ No PHP errors detected</p>\n";
        }
        
        if (empty($pageOutput)) {
            echo "<p style='color: orange;'>⚠ Page output is empty</p>\n";
        } else {
            echo "<p style='color: green;'>✓ Page generated output (" . strlen($pageOutput) . " characters)</p>\n";
            echo "<h3>Page Output Preview (first 500 chars):</h3>\n";
            echo "<pre style='background: #f0f0f0; padding: 10px; border: 1px solid #ccc; max-height: 200px; overflow: auto;'>" . htmlspecialchars(substr($pageOutput, 0, 500)) . "</pre>\n";
        }
        
    } catch (Exception $e) {
        ob_end_clean();
        echo "<p style='color: red;'>✗ Exception during page execution: " . $e->getMessage() . "</p>\n";
    }
    
    restore_error_handler();
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Fatal error: " . $e->getMessage() . "</p>\n";
}

echo "<h2>Debug Summary</h2>\n";
echo "<p>This debug script helps identify why the custom_fields.php page appears blank.</p>\n";

?>
