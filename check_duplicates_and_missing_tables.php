<?php
/**
 * Check for duplicate functionality and missing database tables
 */

require_once 'campaign/church/config.php';

echo "<h1>Database Analysis & Duplicate Detection</h1>\n";

// Check existing tables
echo "<h2>1. Existing Database Tables</h2>\n";
try {
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>All Tables (" . count($tables) . "):</h3>\n";
    echo "<ul>\n";
    foreach ($tables as $table) {
        echo "<li>$table</li>\n";
    }
    echo "</ul>\n";
    
    // Check for SMS-related tables
    echo "<h3>SMS-Related Tables:</h3>\n";
    $smsTables = array_filter($tables, function($table) {
        return strpos($table, 'sms') !== false;
    });
    
    if (empty($smsTables)) {
        echo "<p style='color: red;'>❌ No SMS tables found!</p>\n";
    } else {
        echo "<ul>\n";
        foreach ($smsTables as $table) {
            echo "<li style='color: green;'>✅ $table</li>\n";
        }
        echo "</ul>\n";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>\n";
}

// Check for settings-related tables
echo "<h2>2. Settings Tables Analysis</h2>\n";
$settingsTables = ['site_settings', 'appearance_settings', 'sms_settings', 'security_settings'];

foreach ($settingsTables as $table) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
        $count = $stmt->fetchColumn();
        echo "<p style='color: green;'>✅ $table: $count records</p>\n";
        
        // Show sample settings
        $stmt = $pdo->query("SELECT * FROM $table LIMIT 5");
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        if (!empty($settings)) {
            echo "<details><summary>Sample settings</summary>\n";
            echo "<pre>" . print_r($settings, true) . "</pre>\n";
            echo "</details>\n";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ $table: " . $e->getMessage() . "</p>\n";
    }
}

// Check for duplicate admin pages
echo "<h2>3. Duplicate Admin Pages Analysis</h2>\n";

$adminPages = [
    'settings.php' => 'General system settings',
    'advanced_settings.php' => 'Advanced system settings',
    'site_settings.php' => 'Site-specific settings',
    'custom_fields.php' => 'Custom fields management',
    'logo_upload.php' => 'Logo upload functionality',
    'logo_management.php' => 'Logo management',
    'branding_settings.php' => 'Branding and appearance',
    'appearance_settings.php' => 'Appearance settings',
    'sms_campaigns.php' => 'SMS campaign management',
    'sms_templates.php' => 'SMS template management',
    'sms_analytics.php' => 'SMS analytics',
    'bulk_sms.php' => 'Bulk SMS sending',
    'single_sms.php' => 'Single SMS sending',
    'sms_integration.php' => 'SMS integration settings'
];

echo "<h3>Admin Pages Found:</h3>\n";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
echo "<tr><th>File</th><th>Purpose</th><th>Status</th></tr>\n";

foreach ($adminPages as $page => $purpose) {
    $path = "campaign/church/admin/$page";
    if (file_exists($path)) {
        echo "<tr><td>$page</td><td>$purpose</td><td style='color: green;'>✅ Exists</td></tr>\n";
    } else {
        echo "<tr><td>$page</td><td>$purpose</td><td style='color: red;'>❌ Missing</td></tr>\n";
    }
}
echo "</table>\n";

// Identify potential duplicates
echo "<h2>4. Potential Duplicates Identified</h2>\n";
echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>\n";
echo "<h3>🔄 Settings Management Duplicates:</h3>\n";
echo "<ul>\n";
echo "<li><strong>settings.php</strong> vs <strong>advanced_settings.php</strong> vs <strong>site_settings.php</strong></li>\n";
echo "<li>All three handle similar system configuration</li>\n";
echo "</ul>\n";

echo "<h3>🔄 Logo Management Duplicates:</h3>\n";
echo "<ul>\n";
echo "<li><strong>logo_upload.php</strong> vs <strong>logo_management.php</strong></li>\n";
echo "<li>Both handle logo upload and management</li>\n";
echo "</ul>\n";

echo "<h3>🔄 Branding/Appearance Duplicates:</h3>\n";
echo "<ul>\n";
echo "<li><strong>branding_settings.php</strong> vs <strong>appearance_settings.php</strong></li>\n";
echo "<li>Both handle visual customization</li>\n";
echo "</ul>\n";

echo "<h3>🔄 SMS Management Duplicates:</h3>\n";
echo "<ul>\n";
echo "<li><strong>bulk_sms.php</strong> vs <strong>single_sms.php</strong> - Could be combined</li>\n";
echo "<li><strong>sms_campaigns.php</strong> vs <strong>sms_templates.php</strong> - Related functionality</li>\n";
echo "</ul>\n";
echo "</div>\n";

// Missing tables that need to be created
echo "<h2>5. Missing Tables That Need Creation</h2>\n";
$requiredTables = [
    'sms_campaigns' => 'SMS campaign management',
    'sms_templates' => 'SMS template storage',
    'sms_campaign_recipients' => 'SMS campaign recipient tracking',
    'sms_logs' => 'SMS sending logs'
];

echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
echo "<h3>❌ Missing SMS Tables:</h3>\n";
foreach ($requiredTables as $table => $purpose) {
    try {
        $stmt = $pdo->query("SELECT 1 FROM $table LIMIT 1");
        echo "<p style='color: green;'>✅ $table - $purpose</p>\n";
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ $table - $purpose (MISSING)</p>\n";
    }
}
echo "</div>\n";

echo "<h2>6. Recommended Cleanup Actions</h2>\n";
echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px;'>\n";
echo "<ol>\n";
echo "<li><strong>Consolidate Settings:</strong> Merge settings.php, advanced_settings.php, and site_settings.php into one comprehensive settings page</li>\n";
echo "<li><strong>Merge Logo Management:</strong> Combine logo_upload.php and logo_management.php</li>\n";
echo "<li><strong>Unify Branding:</strong> Merge branding_settings.php and appearance_settings.php</li>\n";
echo "<li><strong>Create Missing SMS Tables:</strong> Set up the missing SMS infrastructure</li>\n";
echo "<li><strong>Combine SMS Pages:</strong> Merge bulk_sms.php and single_sms.php into one SMS management interface</li>\n";
echo "</ol>\n";
echo "</div>\n";

?>
