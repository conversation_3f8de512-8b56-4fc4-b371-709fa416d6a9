<?php
session_start();
ini_set('display_errors', 0); // Don't display PHP errors directly
error_reporting(E_ALL); // Still report all errors for logging

// Create a custom error log function
function log_admin_error($message, $error_level = 'ERROR') {
    $log_file = __DIR__ . '/../logs/admin_errors.log';
    $timestamp = date('Y-m-d H:i:s');
    $user_id = isset($_SESSION['admin_id']) ? $_SESSION['admin_id'] : 'NOT_LOGGED_IN';
    $log_message = "[$timestamp] [$error_level] [Admin ID: $user_id] [Page: " . basename($_SERVER['PHP_SELF']) . "] $message" . PHP_EOL;
    
    // Create logs directory if it doesn't exist
    if (!is_dir(dirname($log_file))) {
        mkdir(dirname($log_file), 0755, true);
    }
    
    // Append to log file
    file_put_contents($log_file, $log_message, FILE_APPEND);
}

// Set up custom error handler
function custom_error_handler($errno, $errstr, $errfile, $errline) {
    $error_message = "PHP Error [$errno]: $errstr in $errfile on line $errline";
    log_admin_error($error_message);
    
    global $error;
    $error = "System error occurred. Please check the error logs or contact support.";
    return true;
}
set_error_handler('custom_error_handler');

// Set up exception handler
function custom_exception_handler($exception) {
    $error_message = "Uncaught Exception: " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine();
    log_admin_error($error_message);
    
    global $error;
    $error = "Error: " . $exception->getMessage();
}
set_exception_handler('custom_exception_handler');

// Load configuration
try {
    require_once '../config.php';
} catch (Exception $e) {
    log_admin_error("Failed to load config: " . $e->getMessage());
    die("Critical error: Could not load configuration file.");
}

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Page variables
$page_title = 'Site Settings';
$page_header = 'Site Settings';
$page_description = 'Configure general settings for your website and admin panel.';
$message = '';
$error = '';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Log the start of form processing
        log_admin_error("Processing site settings form submission", "INFO");
        
        // Validate and sanitize input
        $site_title = trim($_POST['site_title']);
        $admin_title = trim($_POST['admin_title']);
        $site_description = trim($_POST['site_description']);
        $site_keywords = trim($_POST['site_keywords']);
        $contact_email = filter_var(trim($_POST['contact_email']), FILTER_VALIDATE_EMAIL);
        $contact_phone = trim($_POST['contact_phone']);
        $church_address = trim($_POST['church_address']);
        $church_service_times = trim($_POST['church_service_times']);
        $footer_text = trim($_POST['footer_text']);
        $social_facebook = trim($_POST['social_facebook']);
        $social_twitter = trim($_POST['social_twitter']);
        $social_instagram = trim($_POST['social_instagram']);
        $social_youtube = trim($_POST['social_youtube']);

        // Organization-specific settings
        $organization_type = trim($_POST['organization_type']);
        $organization_name = trim($_POST['organization_name']);
        $organization_mission = trim($_POST['organization_mission']);
        $organization_vision = trim($_POST['organization_vision']);
        $organization_values = trim($_POST['organization_values']);
        $member_term = trim($_POST['member_term']);
        $leader_term = trim($_POST['leader_term']);
        $group_term = trim($_POST['group_term']);
        $event_term = trim($_POST['event_term']);
        $donation_term = trim($_POST['donation_term']);
        
        // Validate required fields
        if (empty($site_title)) {
            throw new Exception('Site title is required');
        }

        if (empty($admin_title)) {
            throw new Exception('Admin panel title is required');
        }

        if (empty($organization_type)) {
            throw new Exception('Organization type is required');
        }

        if (empty($organization_name)) {
            throw new Exception('Organization name is required');
        }
        
        if (!empty($_POST['contact_email']) && !$contact_email) {
            throw new Exception('Please enter a valid email address');
        }
        
        // Create a transaction to ensure all updates happen together
        log_admin_error("Starting transaction for settings update", "INFO");
        $pdo->beginTransaction();
        
        try {
            // Update settings
            update_site_setting('site_title', $site_title);
            update_site_setting('admin_title', $admin_title);
            update_site_setting('site_description', $site_description);
            update_site_setting('site_keywords', $site_keywords);
            
            // Handle contact_email which is an email-related setting
            update_site_setting('contact_email', $contact_email);
            update_email_related_settings('contact_email', $contact_email);
            
            update_site_setting('contact_phone', $contact_phone);
            update_site_setting('church_address', $church_address);
            update_site_setting('church_service_times', $church_service_times);
            update_site_setting('footer_text', $footer_text);
            update_site_setting('social_facebook', $social_facebook);
            update_site_setting('social_twitter', $social_twitter);
            update_site_setting('social_instagram', $social_instagram);
            update_site_setting('social_youtube', $social_youtube);

            // Update organization-specific settings
            update_site_setting('organization_type', $organization_type);
            update_site_setting('organization_name', $organization_name);
            update_site_setting('organization_mission', $organization_mission);
            update_site_setting('organization_vision', $organization_vision);
            update_site_setting('organization_values', $organization_values);
            update_site_setting('member_term', $member_term);
            update_site_setting('leader_term', $leader_term);
            update_site_setting('group_term', $group_term);
            update_site_setting('event_term', $event_term);
            update_site_setting('donation_term', $donation_term);
            
            // Commit transaction
            $pdo->commit();
            log_admin_error("Settings update transaction committed successfully", "INFO");
            
            // Set success message
            $message = 'Settings updated successfully.';
        } catch (PDOException $e) {
            // Rollback transaction on error
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            log_admin_error("Settings update transaction rolled back due to error: " . $e->getMessage());
            $error = "Error updating settings: " . $e->getMessage();
        }
    } catch (Exception $e) {
        log_admin_error("Error in site settings form handling: " . $e->getMessage(), "ERROR");
        $error = $e->getMessage();
    }
}

// Get current settings
$site_title = get_site_setting('site_title', 'Freedom Assembly Church');
$admin_title = get_site_setting('admin_title', 'Church Admin');
$site_description = get_site_setting('site_description', '');
$site_keywords = get_site_setting('site_keywords', '');
$contact_email = get_site_setting('contact_email', '');
$contact_phone = get_site_setting('contact_phone', '');
$church_address = get_site_setting('church_address', '');
$church_service_times = get_site_setting('church_service_times', '');
$footer_text = get_site_setting('footer_text', '© ' . date('Y') . ' ' . $site_title . '. All rights reserved.');
$social_facebook = get_site_setting('social_facebook', '');
$social_twitter = get_site_setting('social_twitter', '');
$social_instagram = get_site_setting('social_instagram', '');
$social_youtube = get_site_setting('social_youtube', '');

// Get organization-specific settings
$organization_type = get_site_setting('organization_type', 'church');
$organization_name = get_site_setting('organization_name', $site_title);
$organization_mission = get_site_setting('organization_mission', '');
$organization_vision = get_site_setting('organization_vision', '');
$organization_values = get_site_setting('organization_values', '');
$member_term = get_site_setting('member_term', 'Member');
$leader_term = get_site_setting('leader_term', 'Pastor');
$group_term = get_site_setting('group_term', 'Ministry');
$event_term = get_site_setting('event_term', 'Service');
$donation_term = get_site_setting('donation_term', 'Offering');

// Include header
include 'includes/header.php';

// Display success/error messages
if (!empty($message)) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i>' . htmlspecialchars($message) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}

if (!empty($error)) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>' . htmlspecialchars($error) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
    
    // Add debug information for admins if available
    if (file_exists(__DIR__ . '/../logs/admin_errors.log')) {
        echo '<div class="alert alert-warning mt-2">
                <h6><i class="bi bi-info-circle-fill me-2"></i>Troubleshooting Information</h6>
                <p class="mb-1">Error details have been logged. Check the following:</p>
                <ul class="mb-0">
                    <li>Database connection issues</li>
                    <li>File permissions (logs directory should be writable)</li>
                    <li>Check logs at: <code>logs/admin_errors.log</code></li>
                </ul>
              </div>';
    }
}

// Note: We're using the update_site_setting function from config.php instead of defining it here

// Let's enhance the existing update_site_setting function with support for email settings
function update_email_related_settings($key, $value) {
    global $pdo;
    
    // If this is an email-related setting, also update in email_settings table
    $email_related_keys = ['contact_email', 'email_contact_email'];
    
    if (!in_array($key, $email_related_keys)) {
        return; // Not an email-related setting, do nothing
    }
    
    log_admin_error("Processing email-related setting: $key", "INFO");
    
    try {
        // Determine the key for the email_settings table
        $email_settings_key = $key;
        if (strpos($key, 'email_') === 0) {
            $email_settings_key = str_replace('email_', '', $key);
        }
        
        // Check if email_settings table exists
        $check_table = $pdo->query("SHOW TABLES LIKE 'email_settings'");
        if ($check_table->rowCount() > 0) {
            // Table exists, update the email setting
            $stmt = $pdo->prepare("INSERT INTO email_settings (setting_key, setting_value) 
                                VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
            $stmt->execute([$email_settings_key, $value]);
            log_admin_error("Updated email_settings table for key: $email_settings_key", "INFO");
        } else {
            log_admin_error("email_settings table does not exist, skipping email setting update", "WARNING");
        }
    } catch (PDOException $emailError) {
        // Log the error but continue execution
        log_admin_error("Error updating email setting: " . $emailError->getMessage());
    }
    
    try {
        // If the key starts with 'email_', also update the non-prefixed version in settings
        if (strpos($key, 'email_') === 0) {
            $non_prefixed_key = str_replace('email_', '', $key);
            update_site_setting($non_prefixed_key, $value);
            log_admin_error("Updated non-prefixed version for key: $non_prefixed_key", "INFO");
        } 
        // If the key doesn't start with 'email_', also update the prefixed version in settings
        else {
            $prefixed_key = 'email_' . $key;
            update_site_setting($prefixed_key, $value);
            log_admin_error("Updated prefixed version for key: $prefixed_key", "INFO");
        }
    } catch (PDOException $prefixError) {
        // Log the error but continue execution
        log_admin_error("Error updating prefixed/non-prefixed settings: " . $prefixError->getMessage());
    }
}
?>

<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 font-weight-bold text-primary">General Settings</h6>
    </div>
    <div class="card-body">
        <form method="post" id="site-settings-form">
            <!-- General Settings -->
            <div class="row mb-4">
                <div class="col-lg-6">
                    <h5 class="mb-3 border-bottom pb-2">Website Identity</h5>
                    
                    <div class="mb-3">
                        <label for="site_title" class="form-label">Site Title <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="site_title" name="site_title" value="<?php echo htmlspecialchars($site_title); ?>" required>
                        <div class="form-text">The name of your church or organization.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="admin_title" class="form-label">Admin Panel Title <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="admin_title" name="admin_title" value="<?php echo htmlspecialchars($admin_title); ?>" required>
                        <div class="form-text">The title displayed in the admin sidebar.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="site_description" class="form-label">Site Description</label>
                        <textarea class="form-control" id="site_description" name="site_description" rows="3"><?php echo htmlspecialchars($site_description); ?></textarea>
                        <div class="form-text">A short description of your church or organization.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="site_keywords" class="form-label">Site Keywords</label>
                        <input type="text" class="form-control" id="site_keywords" name="site_keywords" value="<?php echo htmlspecialchars($site_keywords); ?>">
                        <div class="form-text">Comma-separated keywords for SEO.</div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <h5 class="mb-3 border-bottom pb-2">Contact Information</h5>
                    
                    <div class="mb-3">
                        <label for="contact_email" class="form-label">Contact Email</label>
                        <input type="email" class="form-control" id="contact_email" name="contact_email" value="<?php echo htmlspecialchars($contact_email); ?>">
                        <div class="form-text">Primary contact email for the church.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="contact_phone" class="form-label">Contact Phone</label>
                        <input type="text" class="form-control" id="contact_phone" name="contact_phone" value="<?php echo htmlspecialchars($contact_phone); ?>">
                        <div class="form-text">Primary contact phone number.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="church_address" class="form-label">Church Address</label>
                        <textarea class="form-control" id="church_address" name="church_address" rows="3"><?php echo htmlspecialchars($church_address); ?></textarea>
                        <div class="form-text">Physical address of your church.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="church_service_times" class="form-label">Service Times</label>
                        <textarea class="form-control" id="church_service_times" name="church_service_times" rows="3"><?php echo htmlspecialchars($church_service_times); ?></textarea>
                        <div class="form-text">Service times for your church (e.g., "Sunday: 9:00 AM, 11:00 AM").</div>
                    </div>
                </div>
            </div>
            
            <!-- Footer & Social Media Settings -->
            <div class="row mb-4">
                <div class="col-lg-6">
                    <h5 class="mb-3 border-bottom pb-2">Footer Settings</h5>
                    
                    <div class="mb-3">
                        <label for="footer_text" class="form-label">Footer Text</label>
                        <textarea class="form-control" id="footer_text" name="footer_text" rows="3"><?php echo htmlspecialchars($footer_text); ?></textarea>
                        <div class="form-text">Text that appears in the footer of your website.</div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <h5 class="mb-3 border-bottom pb-2">Social Media</h5>
                    
                    <div class="mb-3">
                        <label for="social_facebook" class="form-label">
                            <i class="bi bi-facebook text-primary"></i> Facebook URL
                        </label>
                        <input type="url" class="form-control" id="social_facebook" name="social_facebook" value="<?php echo htmlspecialchars($social_facebook); ?>">
                    </div>
                    
                    <div class="mb-3">
                        <label for="social_twitter" class="form-label">
                            <i class="bi bi-twitter text-info"></i> Twitter URL
                        </label>
                        <input type="url" class="form-control" id="social_twitter" name="social_twitter" value="<?php echo htmlspecialchars($social_twitter); ?>">
                    </div>
                    
                    <div class="mb-3">
                        <label for="social_instagram" class="form-label">
                            <i class="bi bi-instagram text-danger"></i> Instagram URL
                        </label>
                        <input type="url" class="form-control" id="social_instagram" name="social_instagram" value="<?php echo htmlspecialchars($social_instagram); ?>">
                    </div>
                    
                    <div class="mb-3">
                        <label for="social_youtube" class="form-label">
                            <i class="bi bi-youtube text-danger"></i> YouTube URL
                        </label>
                        <input type="url" class="form-control" id="social_youtube" name="social_youtube" value="<?php echo htmlspecialchars($social_youtube); ?>">
                    </div>
                </div>
            </div>

            <!-- Organization Settings -->
            <div class="row mb-4">
                <div class="col-12">
                    <h5 class="mb-3 border-bottom pb-2">Organization Settings</h5>
                    <p class="text-muted mb-4">Configure your organization type and terminology to make the system work for churches, schools, businesses, or any other organization.</p>
                </div>

                <div class="col-lg-6">
                    <div class="mb-3">
                        <label for="organization_type" class="form-label">Organization Type <span class="text-danger">*</span></label>
                        <select class="form-select" id="organization_type" name="organization_type" required>
                            <option value="">Select Organization Type</option>
                            <option value="church" <?php echo ($organization_type === 'church') ? 'selected' : ''; ?>>Church</option>
                            <option value="school" <?php echo ($organization_type === 'school') ? 'selected' : ''; ?>>School</option>
                            <option value="business" <?php echo ($organization_type === 'business') ? 'selected' : ''; ?>>Business</option>
                            <option value="nonprofit" <?php echo ($organization_type === 'nonprofit') ? 'selected' : ''; ?>>Non-Profit</option>
                            <option value="association" <?php echo ($organization_type === 'association') ? 'selected' : ''; ?>>Association</option>
                            <option value="club" <?php echo ($organization_type === 'club') ? 'selected' : ''; ?>>Club</option>
                            <option value="community" <?php echo ($organization_type === 'community') ? 'selected' : ''; ?>>Community Group</option>
                            <option value="other" <?php echo ($organization_type === 'other') ? 'selected' : ''; ?>>Other</option>
                        </select>
                        <div class="form-text">This affects default terminology and features throughout the system.</div>
                    </div>

                    <div class="mb-3">
                        <label for="organization_name" class="form-label">Organization Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="organization_name" name="organization_name" value="<?php echo htmlspecialchars($organization_name); ?>" required>
                        <div class="form-text">The full name of your organization.</div>
                    </div>

                    <div class="mb-3">
                        <label for="organization_mission" class="form-label">Mission Statement</label>
                        <textarea class="form-control" id="organization_mission" name="organization_mission" rows="3"><?php echo htmlspecialchars($organization_mission); ?></textarea>
                        <div class="form-text">Your organization's mission statement.</div>
                    </div>

                    <div class="mb-3">
                        <label for="organization_vision" class="form-label">Vision Statement</label>
                        <textarea class="form-control" id="organization_vision" name="organization_vision" rows="3"><?php echo htmlspecialchars($organization_vision); ?></textarea>
                        <div class="form-text">Your organization's vision statement.</div>
                    </div>

                    <div class="mb-3">
                        <label for="organization_values" class="form-label">Core Values</label>
                        <textarea class="form-control" id="organization_values" name="organization_values" rows="3"><?php echo htmlspecialchars($organization_values); ?></textarea>
                        <div class="form-text">Your organization's core values (one per line or comma-separated).</div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <h6 class="mb-3">Custom Terminology</h6>
                    <p class="text-muted small mb-3">Customize the terms used throughout the system to match your organization type.</p>

                    <div class="mb-3">
                        <label for="member_term" class="form-label">Member Term</label>
                        <input type="text" class="form-control" id="member_term" name="member_term" value="<?php echo htmlspecialchars($member_term); ?>" placeholder="e.g., Member, Student, Employee, Participant">
                        <div class="form-text">What do you call your members? (e.g., Members, Students, Employees)</div>
                    </div>

                    <div class="mb-3">
                        <label for="leader_term" class="form-label">Leader Term</label>
                        <input type="text" class="form-control" id="leader_term" name="leader_term" value="<?php echo htmlspecialchars($leader_term); ?>" placeholder="e.g., Pastor, Principal, Manager, President">
                        <div class="form-text">What do you call your leaders? (e.g., Pastor, Principal, Manager)</div>
                    </div>

                    <div class="mb-3">
                        <label for="group_term" class="form-label">Group Term</label>
                        <input type="text" class="form-control" id="group_term" name="group_term" value="<?php echo htmlspecialchars($group_term); ?>" placeholder="e.g., Ministry, Department, Team, Committee">
                        <div class="form-text">What do you call your groups? (e.g., Ministry, Department, Team)</div>
                    </div>

                    <div class="mb-3">
                        <label for="event_term" class="form-label">Event Term</label>
                        <input type="text" class="form-control" id="event_term" name="event_term" value="<?php echo htmlspecialchars($event_term); ?>" placeholder="e.g., Service, Class, Meeting, Event">
                        <div class="form-text">What do you call your events? (e.g., Service, Class, Meeting)</div>
                    </div>

                    <div class="mb-3">
                        <label for="donation_term" class="form-label">Donation Term</label>
                        <input type="text" class="form-control" id="donation_term" name="donation_term" value="<?php echo htmlspecialchars($donation_term); ?>" placeholder="e.g., Offering, Donation, Contribution, Fee">
                        <div class="form-text">What do you call donations/payments? (e.g., Offering, Donation, Fee)</div>
                    </div>
                </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="reset" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-counterclockwise"></i> Reset
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-save"></i> Save Settings
                </button>
            </div>
        </form>
    </div>
</div>

<script src="<?php echo ADMIN_URL; ?>/assets/js/organization-type-handler.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.getElementById('site-settings-form');
    
    form.addEventListener('submit', function(event) {
        let isValid = true;
        
        // Site title validation
        const siteTitle = document.getElementById('site_title');
        if (!siteTitle.value.trim()) {
            isValid = false;
            siteTitle.classList.add('is-invalid');
        } else {
            siteTitle.classList.remove('is-invalid');
        }
        
        // Admin title validation
        const adminTitle = document.getElementById('admin_title');
        if (!adminTitle.value.trim()) {
            isValid = false;
            adminTitle.classList.add('is-invalid');
        } else {
            adminTitle.classList.remove('is-invalid');
        }

        // Organization type validation
        const organizationType = document.getElementById('organization_type');
        if (!organizationType.value.trim()) {
            isValid = false;
            organizationType.classList.add('is-invalid');
        } else {
            organizationType.classList.remove('is-invalid');
        }

        // Organization name validation
        const organizationName = document.getElementById('organization_name');
        if (!organizationName.value.trim()) {
            isValid = false;
            organizationName.classList.add('is-invalid');
        } else {
            organizationName.classList.remove('is-invalid');
        }
        
        // Email validation
        const contactEmail = document.getElementById('contact_email');
        if (contactEmail.value.trim() && !validateEmail(contactEmail.value.trim())) {
            isValid = false;
            contactEmail.classList.add('is-invalid');
        } else {
            contactEmail.classList.remove('is-invalid');
        }
        
        if (!isValid) {
            event.preventDefault();
        }
    });
    
    // Email validation helper
    function validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }
    
    // Reset form button
    document.querySelector('button[type="reset"]').addEventListener('click', function() {
        setTimeout(function() {
            document.querySelectorAll('.is-invalid').forEach(function(el) {
                el.classList.remove('is-invalid');
            });
        }, 10);
    });
});
</script>

<?php include 'includes/footer.php'; ?> 