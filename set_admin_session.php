<?php
/**
 * Set Admin Session for Testing
 * This script sets up the admin session so the original admin pages work
 */

session_start();

// Set admin session variables
$_SESSION['admin_id'] = 4;
$_SESSION['admin_username'] = 'admin';
$_SESSION['admin_email'] = '<EMAIL>';
$_SESSION['admin_full_name'] = 'Church Administrator';
$_SESSION['admin_role'] = 'admin';
$_SESSION['CREATED'] = time();
$_SESSION['LAST_ACTIVITY'] = time();

echo "<h1>Admin Session Set Successfully!</h1>\n";
echo "<p>You can now access the original admin pages:</p>\n";
echo "<ul>\n";
echo "<li><a href='campaign/church/admin/custom_fields.php' target='_blank'>Original Custom Fields Page</a></li>\n";
echo "<li><a href='campaign/church/admin/logo_upload.php' target='_blank'>Original Logo Upload Page</a></li>\n";
echo "<li><a href='campaign/church/admin/branding_settings.php' target='_blank'>Original Branding Settings Page</a></li>\n";
echo "</ul>\n";

echo "<h2>Session Information:</h2>\n";
echo "<ul>\n";
foreach ($_SESSION as $key => $value) {
    echo "<li><strong>$key:</strong> " . htmlspecialchars($value) . "</li>\n";
}
echo "</ul>\n";

echo "<p><em>Note: This session will persist until you close your browser or it times out.</em></p>\n";
?>
