<?php
/**
 * Logo Upload Management
 * Enhanced logo management with file upload capabilities
 */

// Start session first
session_start();

// Set admin session if not already set (for testing)
if (!isset($_SESSION['admin_id'])) {
    $_SESSION['admin_id'] = 4;
    $_SESSION['admin_username'] = 'admin';
    $_SESSION['admin_email'] = '<EMAIL>';
    $_SESSION['admin_full_name'] = 'Church Administrator';
    $_SESSION['admin_role'] = 'admin';
    $_SESSION['CREATED'] = time();
    $_SESSION['LAST_ACTIVITY'] = time();
}

// Include database config
require_once '../config.php';

// Set page title
$page_title = 'Logo Upload Management';

require_once 'includes/header.php';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'upload_logo':
                    $uploadDir = 'uploads/logos/';
                    if (!file_exists($uploadDir)) {
                        mkdir($uploadDir, 0755, true);
                    }
                    
                    if (isset($_FILES['logo_file']) && $_FILES['logo_file']['error'] === UPLOAD_ERR_OK) {
                        $file = $_FILES['logo_file'];
                        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml'];
                        $maxSize = 5 * 1024 * 1024; // 5MB
                        
                        // Validate file type
                        if (!in_array($file['type'], $allowedTypes)) {
                            throw new Exception('Invalid file type. Please upload a JPEG, PNG, GIF, or SVG image.');
                        }
                        
                        // Validate file size
                        if ($file['size'] > $maxSize) {
                            throw new Exception('File size too large. Maximum size is 5MB.');
                        }
                        
                        // Generate unique filename
                        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                        $filename = 'logo_' . time() . '_' . uniqid() . '.' . $extension;
                        $filepath = $uploadDir . $filename;
                        
                        // Move uploaded file
                        if (move_uploaded_file($file['tmp_name'], $filepath)) {
                            // Update database
                            $stmt = $pdo->prepare("
                                INSERT INTO appearance_settings (setting_name, setting_value, updated_at)
                                VALUES ('logo_url', ?, NOW())
                                ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()
                            ");
                            $stmt->execute([$filepath]);
                            
                            // Delete old logo if exists
                            if (!empty($_POST['old_logo']) && file_exists($_POST['old_logo'])) {
                                unlink($_POST['old_logo']);
                            }
                            
                            $success_message = "Logo uploaded successfully!";
                        } else {
                            throw new Exception('Failed to upload file.');
                        }
                    } else {
                        throw new Exception('No file uploaded or upload error occurred.');
                    }
                    break;
                    
                case 'update_logo_settings':
                    $settings = [
                        'logo_width' => (int)$_POST['logo_width'],
                        'logo_height' => (int)$_POST['logo_height'],
                        'logo_position' => $_POST['logo_position'],
                        'show_logo_text' => isset($_POST['show_logo_text']) ? 1 : 0,
                        'logo_text' => $_POST['logo_text']
                    ];
                    
                    foreach ($settings as $key => $value) {
                        $stmt = $pdo->prepare("
                            INSERT INTO appearance_settings (setting_name, setting_value, updated_at)
                            VALUES (?, ?, NOW())
                            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()
                        ");
                        $stmt->execute([$key, $value]);
                    }
                    
                    $success_message = "Logo settings updated successfully!";
                    break;
                    
                case 'delete_logo':
                    $stmt = $pdo->prepare("SELECT setting_value FROM appearance_settings WHERE setting_name = 'logo_url'");
                    $stmt->execute();
                    $logoPath = $stmt->fetchColumn();
                    
                    if ($logoPath && file_exists($logoPath)) {
                        unlink($logoPath);
                    }
                    
                    $stmt = $pdo->prepare("DELETE FROM appearance_settings WHERE setting_name = 'logo_url'");
                    $stmt->execute();
                    
                    $success_message = "Logo deleted successfully!";
                    break;
            }
        }
    } catch (Exception $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}

// Get current logo settings
$stmt = $pdo->prepare("SELECT setting_name, setting_value FROM appearance_settings WHERE setting_name IN ('logo_url', 'logo_width', 'logo_height', 'logo_position', 'show_logo_text', 'logo_text')");
$stmt->execute();
$settings = [];
while ($row = $stmt->fetch()) {
    $settings[$row['setting_name']] = $row['setting_value'];
}

$page_title = "Logo Management";
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-image"></i> Logo Management
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-outline-secondary" onclick="previewLogo()">
            <i class="bi bi-eye"></i> Preview
        </button>
    </div>
</div>

<?php if (isset($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <!-- Current Logo Display -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-image"></i> Current Logo
                </h5>
            </div>
            <div class="card-body text-center">
                <?php if (!empty($settings['logo_url']) && file_exists($settings['logo_url'])): ?>
                    <div class="logo-preview mb-3">
                        <img src="<?php echo htmlspecialchars($settings['logo_url']); ?>" 
                             alt="Current Logo" 
                             class="img-fluid"
                             style="max-width: 200px; max-height: 150px;">
                    </div>
                    <div class="logo-info">
                        <small class="text-muted">
                            File: <?php echo basename($settings['logo_url']); ?><br>
                            Size: <?php echo file_exists($settings['logo_url']) ? number_format(filesize($settings['logo_url']) / 1024, 1) . ' KB' : 'Unknown'; ?>
                        </small>
                    </div>
                    <div class="mt-3">
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="delete_logo">
                            <button type="submit" class="btn btn-outline-danger btn-sm" 
                                    onclick="return confirm('Are you sure you want to delete the current logo?')">
                                <i class="bi bi-trash"></i> Delete Logo
                            </button>
                        </form>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-image display-1 text-muted"></i>
                        <h6 class="mt-2">No Logo Uploaded</h6>
                        <p class="text-muted small">Upload a logo to customize your branding</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Logo Upload Form -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-upload"></i> Upload New Logo
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="upload_logo">
                    <input type="hidden" name="old_logo" value="<?php echo htmlspecialchars($settings['logo_url'] ?? ''); ?>">
                    
                    <div class="mb-3">
                        <label for="logo_file" class="form-label">Select Logo File</label>
                        <input type="file" class="form-control" id="logo_file" name="logo_file" 
                               accept="image/jpeg,image/png,image/gif,image/svg+xml" required>
                        <div class="form-text">
                            Supported formats: JPEG, PNG, GIF, SVG. Maximum size: 5MB.<br>
                            Recommended dimensions: 200x100 pixels or similar aspect ratio.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="upload-preview" id="uploadPreview" style="display: none;">
                            <img id="previewImage" src="" alt="Preview" class="img-fluid" style="max-width: 200px; max-height: 150px;">
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-upload"></i> Upload Logo
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Logo Settings -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-sliders"></i> Logo Display Settings
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_logo_settings">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="logo_width" class="form-label">Logo Width (px)</label>
                            <input type="number" class="form-control" id="logo_width" name="logo_width" 
                                   value="<?php echo htmlspecialchars($settings['logo_width'] ?? '150'); ?>" 
                                   min="50" max="500">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="logo_height" class="form-label">Logo Height (px)</label>
                            <input type="number" class="form-control" id="logo_height" name="logo_height" 
                                   value="<?php echo htmlspecialchars($settings['logo_height'] ?? '75'); ?>" 
                                   min="25" max="250">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="logo_position" class="form-label">Logo Position</label>
                        <select class="form-select" id="logo_position" name="logo_position">
                            <option value="left" <?php echo ($settings['logo_position'] ?? 'left') === 'left' ? 'selected' : ''; ?>>Left</option>
                            <option value="center" <?php echo ($settings['logo_position'] ?? '') === 'center' ? 'selected' : ''; ?>>Center</option>
                            <option value="right" <?php echo ($settings['logo_position'] ?? '') === 'right' ? 'selected' : ''; ?>>Right</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="show_logo_text" name="show_logo_text" 
                                   <?php echo ($settings['show_logo_text'] ?? 0) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="show_logo_text">
                                Show text alongside logo
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="logo_text" class="form-label">Logo Text</label>
                        <input type="text" class="form-control" id="logo_text" name="logo_text" 
                               value="<?php echo htmlspecialchars($settings['logo_text'] ?? 'Church Management System'); ?>"
                               placeholder="Enter text to display with logo">
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> Save Settings
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Preview uploaded image
document.getElementById('logo_file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('previewImage').src = e.target.result;
            document.getElementById('uploadPreview').style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        document.getElementById('uploadPreview').style.display = 'none';
    }
});

// Preview logo function
function previewLogo() {
    const logoUrl = '<?php echo htmlspecialchars($settings['logo_url'] ?? ''); ?>';
    if (logoUrl) {
        window.open(logoUrl, '_blank');
    } else {
        alert('No logo uploaded yet.');
    }
}

// Auto-adjust height when width changes (maintain aspect ratio)
document.getElementById('logo_width').addEventListener('input', function() {
    const width = parseInt(this.value);
    const height = Math.round(width * 0.5); // 2:1 aspect ratio
    document.getElementById('logo_height').value = height;
});
</script>

<?php include 'includes/footer.php'; ?>
