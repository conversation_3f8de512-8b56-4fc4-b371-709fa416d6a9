<?php
/**
 * Fix SMS Table Column Names to Match Page Expectations
 * This script renames columns to match what the SMS pages expect
 */

require_once 'campaign/church/config.php';

echo "<h1>Fixing SMS Table Column Names</h1>\n";

try {
    echo "<h2>1. Fixing sms_templates table columns...</h2>\n";
    
    // Check current columns
    $stmt = $pdo->query("DESCRIBE sms_templates");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $existingColumns = array_column($columns, 'Field');
    
    // Rename 'name' to 'template_name' if needed
    if (in_array('name', $existingColumns) && !in_array('template_name', $existingColumns)) {
        $pdo->exec("ALTER TABLE sms_templates CHANGE name template_name VARCHAR(255) NOT NULL");
        echo "<p style='color: green;'>✅ Renamed 'name' to 'template_name'</p>\n";
    }
    
    // Rename 'message' to 'message_content' if needed
    if (in_array('message', $existingColumns) && !in_array('message_content', $existingColumns)) {
        $pdo->exec("ALTER TABLE sms_templates CHANGE message message_content TEXT NOT NULL");
        echo "<p style='color: green;'>✅ Renamed 'message' to 'message_content'</p>\n";
    }
    
    echo "<h2>2. Checking sms_campaigns table...</h2>\n";
    
    // Check sms_campaigns columns
    $stmt = $pdo->query("DESCRIBE sms_campaigns");
    $campaignColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $existingCampaignColumns = array_column($campaignColumns, 'Field');
    
    // Rename 'message' to 'message_content' in campaigns if needed
    if (in_array('message', $existingCampaignColumns) && !in_array('message_content', $existingCampaignColumns)) {
        $pdo->exec("ALTER TABLE sms_campaigns CHANGE message message_content TEXT NOT NULL");
        echo "<p style='color: green;'>✅ Renamed 'message' to 'message_content' in sms_campaigns</p>\n";
    }
    
    echo "<h2>3. Final table structures:</h2>\n";
    
    // Show final sms_templates structure
    echo "<h3>sms_templates:</h3>\n";
    $stmt = $pdo->query("DESCRIBE sms_templates");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>\n";
    foreach ($columns as $column) {
        echo "<tr><td>{$column['Field']}</td><td>{$column['Type']}</td><td>{$column['Null']}</td><td>{$column['Key']}</td><td>{$column['Default']}</td></tr>\n";
    }
    echo "</table>\n";
    
    // Test the templates
    echo "<h2>4. Testing template data...</h2>\n";
    $stmt = $pdo->query("SELECT id, template_name, category, message_content FROM sms_templates LIMIT 3");
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($templates) {
        echo "<h3>Sample templates:</h3>\n";
        echo "<table border='1' style='border-collapse: collapse;'>\n";
        echo "<tr><th>ID</th><th>Template Name</th><th>Category</th><th>Message Preview</th></tr>\n";
        foreach ($templates as $template) {
            $preview = substr($template['message_content'], 0, 50) . (strlen($template['message_content']) > 50 ? '...' : '');
            echo "<tr><td>{$template['id']}</td><td>{$template['template_name']}</td><td>{$template['category']}</td><td>{$preview}</td></tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<p style='color: orange;'>⚠️ No templates found in database</p>\n";
    }
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h3 style='color: #155724; margin-top: 0;'>✅ SMS Column Names Fixed!</h3>\n";
    echo "<p style='color: #155724;'>All column names now match what the SMS pages expect.</p>\n";
    echo "</div>\n";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3 style='color: #721c24;'>❌ Error Fixing Column Names</h3>\n";
    echo "<p style='color: #721c24;'>Error: " . $e->getMessage() . "</p>\n";
    echo "</div>\n";
}

echo "<br><p><a href='campaign/church/admin/sms_templates.php'>Test SMS Templates</a> | ";
echo "<a href='campaign/church/admin/sms_campaigns.php'>Test SMS Campaigns</a></p>\n";
?>
