<?php
/**
 * Database Backup Script
 * Creates a backup of the current database before making any changes
 */

require_once 'campaign/church/config.php';

$backupDir = 'database_backups';
if (!is_dir($backupDir)) {
    mkdir($backupDir, 0755, true);
}

$timestamp = date('Y-m-d_H-i-s');
$backupFile = $backupDir . '/campaign_backup_' . $timestamp . '.sql';

echo "<h1>Database Backup Process</h1>\n";
echo "<p>Creating backup of database: $dbname</p>\n";
echo "<p>Backup file: $backupFile</p>\n";

try {
    // Get all tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $backup = "-- Database Backup for $dbname\n";
    $backup .= "-- Created on: " . date('Y-m-d H:i:s') . "\n";
    $backup .= "-- Total tables: " . count($tables) . "\n\n";
    $backup .= "SET FOREIGN_KEY_CHECKS=0;\n";
    $backup .= "SET SQL_MODE = \"NO_AUTO_VALUE_ON_ZERO\";\n";
    $backup .= "SET time_zone = \"+00:00\";\n\n";
    
    foreach ($tables as $table) {
        echo "<p>Backing up table: $table</p>\n";
        
        // Get table structure
        $stmt = $pdo->query("SHOW CREATE TABLE `$table`");
        $createTable = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $backup .= "-- Table structure for table `$table`\n";
        $backup .= "DROP TABLE IF EXISTS `$table`;\n";
        $backup .= $createTable['Create Table'] . ";\n\n";
        
        // Get table data
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM `$table`");
        $count = $stmt->fetch()['count'];
        
        if ($count > 0) {
            $backup .= "-- Dumping data for table `$table`\n";
            $stmt = $pdo->query("SELECT * FROM `$table`");
            
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $columns = array_keys($row);
                $values = array_values($row);
                
                // Escape values
                $escapedValues = array_map(function($value) use ($pdo) {
                    return $value === null ? 'NULL' : $pdo->quote($value);
                }, $values);
                
                $backup .= "INSERT INTO `$table` (`" . implode('`, `', $columns) . "`) VALUES (" . implode(', ', $escapedValues) . ");\n";
            }
            $backup .= "\n";
        }
    }
    
    $backup .= "SET FOREIGN_KEY_CHECKS=1;\n";
    
    // Write backup to file
    if (file_put_contents($backupFile, $backup)) {
        echo "<p style='color: green;'>✓ Backup created successfully: $backupFile</p>\n";
        echo "<p>Backup size: " . formatBytes(filesize($backupFile)) . "</p>\n";
    } else {
        throw new Exception("Failed to write backup file");
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error creating backup: " . $e->getMessage() . "</p>\n";
}

function formatBytes($size, $precision = 2) {
    $base = log($size, 1024);
    $suffixes = array('B', 'KB', 'MB', 'GB', 'TB');
    return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
}

echo "<h2>Backup Complete</h2>\n";
echo "<p>You can now proceed with database synchronization.</p>\n";
?>
