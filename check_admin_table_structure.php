<?php
/**
 * Check Admin Table Structure
 */

require_once 'campaign/church/config.php';

echo "<h1>Admin Table Structure Check</h1>\n";

try {
    // Check table structure
    $stmt = $pdo->query("DESCRIBE admins");
    $columns = $stmt->fetchAll();
    
    echo "<h2>Admin Table Columns</h2>\n";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
    foreach ($columns as $column) {
        echo "<tr>\n";
        echo "<td>{$column['Field']}</td>\n";
        echo "<td>{$column['Type']}</td>\n";
        echo "<td>{$column['Null']}</td>\n";
        echo "<td>{$column['Key']}</td>\n";
        echo "<td>{$column['Default']}</td>\n";
        echo "<td>{$column['Extra']}</td>\n";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    // Get admin data with correct columns
    $stmt = $pdo->query("SELECT * FROM admins LIMIT 5");
    $admins = $stmt->fetchAll();
    
    echo "<h2>Admin Records</h2>\n";
    if (empty($admins)) {
        echo "<p style='color: red;'>No admin records found!</p>\n";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        
        // Dynamic header based on actual columns
        echo "<tr>\n";
        foreach ($columns as $column) {
            echo "<th>{$column['Field']}</th>\n";
        }
        echo "</tr>\n";
        
        // Data rows
        foreach ($admins as $admin) {
            echo "<tr>\n";
            foreach ($columns as $column) {
                $value = $admin[$column['Field']] ?? 'NULL';
                if ($column['Field'] === 'password') {
                    $value = '[HIDDEN]';
                }
                echo "<td>" . htmlspecialchars($value) . "</td>\n";
            }
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    // Reset password for first admin
    if (!empty($admins)) {
        $firstAdmin = $admins[0];
        $newPassword = 'admin123';
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        
        echo "<h2>Reset Password</h2>\n";
        
        $stmt = $pdo->prepare("UPDATE admins SET password = ? WHERE id = ?");
        $stmt->execute([$hashedPassword, $firstAdmin['id']]);
        
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
        echo "<h3 style='color: #155724; margin-top: 0;'>✅ Password Reset Complete</h3>\n";
        echo "<p style='color: #155724;'><strong>Login Credentials:</strong></p>\n";
        echo "<ul style='color: #155724;'>\n";
        echo "<li><strong>Username:</strong> {$firstAdmin['username']}</li>\n";
        echo "<li><strong>Email:</strong> {$firstAdmin['email']}</li>\n";
        echo "<li><strong>Password:</strong> $newPassword</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
        echo "<p><a href='campaign/church/admin/login.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Admin Login</a></p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>\n";
}

?>
