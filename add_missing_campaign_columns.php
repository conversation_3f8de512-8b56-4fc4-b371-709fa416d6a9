<?php
/**
 * Add Missing SMS Campaign Columns
 * This script adds columns that the SMS campaigns page expects
 */

require_once 'campaign/church/config.php';

echo "<h1>Adding Missing SMS Campaign Columns</h1>\n";

try {
    echo "<h2>1. Checking current sms_campaigns structure...</h2>\n";
    
    // Check current columns
    $stmt = $pdo->query("DESCRIBE sms_campaigns");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $existingColumns = array_column($columns, 'Field');
    
    echo "<h3>Current columns:</h3>\n";
    echo "<ul>\n";
    foreach ($existingColumns as $column) {
        echo "<li>$column</li>\n";
    }
    echo "</ul>\n";
    
    echo "<h2>2. Adding missing columns...</h2>\n";
    
    // Add delivered_count column
    if (!in_array('delivered_count', $existingColumns)) {
        $pdo->exec("ALTER TABLE sms_campaigns ADD COLUMN delivered_count INT DEFAULT 0 AFTER sent_count");
        echo "<p style='color: green;'>✅ Added delivered_count column</p>\n";
    }
    
    // Add bounced_count column
    if (!in_array('bounced_count', $existingColumns)) {
        $pdo->exec("ALTER TABLE sms_campaigns ADD COLUMN bounced_count INT DEFAULT 0 AFTER delivered_count");
        echo "<p style='color: green;'>✅ Added bounced_count column</p>\n";
    }
    
    // Add opened_count column
    if (!in_array('opened_count', $existingColumns)) {
        $pdo->exec("ALTER TABLE sms_campaigns ADD COLUMN opened_count INT DEFAULT 0 AFTER bounced_count");
        echo "<p style='color: green;'>✅ Added opened_count column</p>\n";
    }
    
    // Add clicked_count column
    if (!in_array('clicked_count', $existingColumns)) {
        $pdo->exec("ALTER TABLE sms_campaigns ADD COLUMN clicked_count INT DEFAULT 0 AFTER opened_count");
        echo "<p style='color: green;'>✅ Added clicked_count column</p>\n";
    }
    
    // Add campaign_type column
    if (!in_array('campaign_type', $existingColumns)) {
        $pdo->exec("ALTER TABLE sms_campaigns ADD COLUMN campaign_type ENUM('immediate', 'scheduled', 'recurring') DEFAULT 'immediate' AFTER recipient_type");
        echo "<p style='color: green;'>✅ Added campaign_type column</p>\n";
    }
    
    // Add sender_name column
    if (!in_array('sender_name', $existingColumns)) {
        $pdo->exec("ALTER TABLE sms_campaigns ADD COLUMN sender_name VARCHAR(255) NULL AFTER campaign_type");
        echo "<p style='color: green;'>✅ Added sender_name column</p>\n";
    }
    
    // Add sender_phone column
    if (!in_array('sender_phone', $existingColumns)) {
        $pdo->exec("ALTER TABLE sms_campaigns ADD COLUMN sender_phone VARCHAR(20) NULL AFTER sender_name");
        echo "<p style='color: green;'>✅ Added sender_phone column</p>\n";
    }
    
    // Update status enum to include 'completed'
    $pdo->exec("ALTER TABLE sms_campaigns MODIFY status ENUM('draft', 'scheduled', 'sending', 'sent', 'completed', 'failed', 'cancelled') DEFAULT 'draft'");
    echo "<p style='color: green;'>✅ Updated status enum to include 'completed'</p>\n";
    
    echo "<h2>3. Final sms_campaigns structure:</h2>\n";
    $stmt = $pdo->query("DESCRIBE sms_campaigns");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>\n";
    foreach ($columns as $column) {
        echo "<tr><td>{$column['Field']}</td><td>{$column['Type']}</td><td>{$column['Null']}</td><td>{$column['Key']}</td><td>{$column['Default']}</td></tr>\n";
    }
    echo "</table>\n";
    
    echo "<h2>4. Creating sample campaign data...</h2>\n";
    
    // Check if we have any campaigns
    $stmt = $pdo->query("SELECT COUNT(*) FROM sms_campaigns");
    $campaignCount = $stmt->fetchColumn();
    
    if ($campaignCount == 0) {
        // Create a sample campaign
        $stmt = $pdo->prepare("
            INSERT INTO sms_campaigns (
                name, description, template_id, message_content, recipient_type, 
                campaign_type, status, total_recipients, sent_count, delivered_count, 
                failed_count, bounced_count, delivery_rate, cost, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            'Welcome Campaign',
            'Welcome message for new members',
            3, // Welcome Message template
            'Welcome to Freedom Assembly Church, {first_name}! We\'re excited to have you join our community.',
            'all_members',
            'immediate',
            'completed',
            150,
            145,
            140,
            5,
            3,
            96.55,
            14.50,
            1
        ]);
        
        echo "<p style='color: green;'>✅ Created sample campaign data</p>\n";
    } else {
        echo "<p style='color: blue;'>ℹ️ Found $campaignCount existing campaigns</p>\n";
    }
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h3 style='color: #155724; margin-top: 0;'>✅ SMS Campaign Columns Added!</h3>\n";
    echo "<p style='color: #155724;'>All required columns have been added to the sms_campaigns table.</p>\n";
    echo "</div>\n";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3 style='color: #721c24;'>❌ Error Adding Campaign Columns</h3>\n";
    echo "<p style='color: #721c24;'>Error: " . $e->getMessage() . "</p>\n";
    echo "</div>\n";
}

echo "<br><p><a href='campaign/church/admin/sms_campaigns.php'>Test SMS Campaigns</a> | ";
echo "<a href='campaign/church/admin/sms_templates.php'>Test SMS Templates</a></p>\n";
?>
