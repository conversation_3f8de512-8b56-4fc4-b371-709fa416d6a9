<?php
/**
 * Check Admin Credentials and Reset if Needed
 */

require_once 'campaign/church/config.php';

echo "<h1>Admin Credentials Check</h1>\n";

try {
    // Get all admin accounts
    $stmt = $pdo->query("SELECT id, username, email, full_name, is_active, created_at FROM admins ORDER BY id");
    $admins = $stmt->fetchAll();
    
    echo "<h2>Current Admin Accounts</h2>\n";
    if (empty($admins)) {
        echo "<p style='color: red;'>No admin accounts found!</p>\n";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Full Name</th><th>Active</th><th>Created</th></tr>\n";
        foreach ($admins as $admin) {
            $active = $admin['is_active'] ? 'Yes' : 'No';
            echo "<tr>\n";
            echo "<td>{$admin['id']}</td>\n";
            echo "<td>{$admin['username']}</td>\n";
            echo "<td>{$admin['email']}</td>\n";
            echo "<td>{$admin['full_name']}</td>\n";
            echo "<td>$active</td>\n";
            echo "<td>{$admin['created_at']}</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    // Reset password for the first admin account
    if (!empty($admins)) {
        $firstAdmin = $admins[0];
        $newPassword = 'admin123';
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        
        echo "<h2>Reset Password for Admin</h2>\n";
        
        $stmt = $pdo->prepare("UPDATE admins SET password = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$hashedPassword, $firstAdmin['id']]);
        
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
        echo "<h3 style='color: #155724; margin-top: 0;'>✅ Password Reset Successful</h3>\n";
        echo "<p style='color: #155724;'><strong>Admin Login Credentials:</strong></p>\n";
        echo "<ul style='color: #155724;'>\n";
        echo "<li><strong>Username:</strong> {$firstAdmin['username']}</li>\n";
        echo "<li><strong>Email:</strong> {$firstAdmin['email']}</li>\n";
        echo "<li><strong>Password:</strong> $newPassword</li>\n";
        echo "</ul>\n";
        echo "<p style='color: #155724;'>You can now log in to the admin panel with these credentials.</p>\n";
        echo "</div>\n";
        
        echo "<h3>Login Links</h3>\n";
        echo "<ul>\n";
        echo "<li><a href='campaign/church/admin/login.php' target='_blank'>Admin Login Page</a></li>\n";
        echo "<li><a href='campaign/church/admin/custom_fields.php' target='_blank'>Custom Fields (after login)</a></li>\n";
        echo "<li><a href='campaign/church/admin/logo_upload.php' target='_blank'>Logo Upload (after login)</a></li>\n";
        echo "<li><a href='campaign/church/admin/branding_settings.php' target='_blank'>Branding Settings (after login)</a></li>\n";
        echo "</ul>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>\n";
}

?>
