<?php
/**
 * Database Synchronization Script
 * This script ensures the database schema matches the codebase expectations
 */

require_once 'campaign/church/config.php';

echo "<h1>Database Synchronization Process</h1>\n";
echo "<h2>Phase 1: Schema Verification</h2>\n";

$syncLog = [];
$errors = [];

try {
    // Test database connection
    $pdo->query("SELECT 1");
    echo "<p style='color: green;'>✓ Database connection verified</p>\n";
    $syncLog[] = "Database connection verified";
    
    // Check if all expected tables exist
    $stmt = $pdo->query("SHOW TABLES");
    $currentTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $expectedTables = [
        'activity_logs', 'admin_2fa', 'admin_activity_logs', 'admin_login_attempts', 'admins',
        'automated_emails_settings', 'calendar_settings', 'contact_email_logs', 'contact_group_members',
        'contact_groups', 'contacts', 'donation_notifications', 'donations', 'email_logs',
        'email_settings', 'email_templates', 'email_tracking', 'members', 'payment_settings',
        'payment_transactions', 'security_logs', 'security_settings', 'settings',
        'whatsapp_logs', 'whatsapp_settings', 'whatsapp_templates'
    ];
    
    $missingTables = array_diff($expectedTables, $currentTables);
    if (empty($missingTables)) {
        echo "<p style='color: green;'>✓ All expected tables exist</p>\n";
        $syncLog[] = "All expected tables verified";
    } else {
        echo "<p style='color: red;'>✗ Missing tables: " . implode(', ', $missingTables) . "</p>\n";
        $errors[] = "Missing tables: " . implode(', ', $missingTables);
    }
    
    echo "<h2>Phase 2: Index Optimization</h2>\n";
    
    // Check and add missing indexes for performance
    $indexChecks = [
        'members' => [
            'idx_email' => 'email',
            'idx_birth_date' => 'birth_date',
            'idx_status' => 'status',
            'idx_created_at' => 'created_at'
        ],
        'email_logs' => [
            'idx_sent_at' => 'sent_at',
            'idx_status' => 'status',
            'idx_email_type' => 'email_type'
        ],
        'email_tracking' => [
            'idx_sent_at' => 'sent_at',
            'idx_opened_at' => 'opened_at'
        ],
        'contacts' => [
            'idx_created_at' => 'created_at',
            'idx_source' => 'source'
        ]
    ];
    
    foreach ($indexChecks as $table => $indexes) {
        if (in_array($table, $currentTables)) {
            echo "<h3>Checking indexes for table: $table</h3>\n";
            
            // Get existing indexes
            $stmt = $pdo->query("SHOW INDEX FROM `$table`");
            $existingIndexes = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $existingIndexes[] = $row['Key_name'];
            }
            
            foreach ($indexes as $indexName => $column) {
                if (!in_array($indexName, $existingIndexes)) {
                    try {
                        $sql = "ALTER TABLE `$table` ADD INDEX `$indexName` (`$column`)";
                        $pdo->exec($sql);
                        echo "<p style='color: green;'>✓ Added index $indexName on $table.$column</p>\n";
                        $syncLog[] = "Added index $indexName on $table.$column";
                    } catch (Exception $e) {
                        echo "<p style='color: orange;'>⚠ Could not add index $indexName on $table.$column: " . $e->getMessage() . "</p>\n";
                        $syncLog[] = "Warning: Could not add index $indexName on $table.$column";
                    }
                } else {
                    echo "<p>✓ Index $indexName already exists on $table</p>\n";
                }
            }
        }
    }
    
    echo "<h2>Phase 3: Data Integrity Checks</h2>\n";
    
    // Check for orphaned records
    $integrityChecks = [
        'email_logs' => [
            'member_id' => 'members.id',
            'template_id' => 'email_templates.id'
        ],
        'email_tracking' => [
            'member_id' => 'members.id'
        ],
        'contact_email_logs' => [
            'recipient_id' => 'contacts.id',
            'template_id' => 'email_templates.id'
        ]
    ];
    
    foreach ($integrityChecks as $table => $foreignKeys) {
        if (in_array($table, $currentTables)) {
            echo "<h3>Checking data integrity for: $table</h3>\n";
            
            foreach ($foreignKeys as $column => $reference) {
                list($refTable, $refColumn) = explode('.', $reference);
                
                if (in_array($refTable, $currentTables)) {
                    $sql = "SELECT COUNT(*) as orphaned FROM `$table` t 
                           LEFT JOIN `$refTable` r ON t.`$column` = r.`$refColumn` 
                           WHERE t.`$column` IS NOT NULL AND r.`$refColumn` IS NULL";
                    
                    $stmt = $pdo->query($sql);
                    $orphaned = $stmt->fetch()['orphaned'];
                    
                    if ($orphaned > 0) {
                        echo "<p style='color: orange;'>⚠ Found $orphaned orphaned records in $table.$column</p>\n";
                        $syncLog[] = "Warning: $orphaned orphaned records in $table.$column";
                    } else {
                        echo "<p style='color: green;'>✓ No orphaned records in $table.$column</p>\n";
                    }
                }
            }
        }
    }
    
    echo "<h2>Phase 4: Application Compatibility Test</h2>\n";
    
    // Test key queries that the application uses
    $testQueries = [
        'Members count' => "SELECT COUNT(*) as count FROM members WHERE status = 'active'",
        'Email templates' => "SELECT COUNT(*) as count FROM email_templates",
        'Recent email logs' => "SELECT COUNT(*) as count FROM email_logs WHERE sent_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)",
        'Contacts count' => "SELECT COUNT(*) as count FROM contacts"
    ];
    
    foreach ($testQueries as $description => $query) {
        try {
            $stmt = $pdo->query($query);
            $result = $stmt->fetch();
            echo "<p style='color: green;'>✓ $description: " . $result['count'] . "</p>\n";
            $syncLog[] = "$description test passed: " . $result['count'];
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ $description test failed: " . $e->getMessage() . "</p>\n";
            $errors[] = "$description test failed: " . $e->getMessage();
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Critical error: " . $e->getMessage() . "</p>\n";
    $errors[] = "Critical error: " . $e->getMessage();
}

// Summary
echo "<h2>Synchronization Summary</h2>\n";

if (empty($errors)) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3 style='color: #155724; margin-top: 0;'>✓ Database Synchronization Successful</h3>\n";
    echo "<p style='color: #155724;'>Your database is now synchronized with the codebase requirements.</p>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3 style='color: #721c24; margin-top: 0;'>⚠ Synchronization Completed with Warnings</h3>\n";
    echo "<p style='color: #721c24;'>The following issues were found:</p>\n";
    echo "<ul style='color: #721c24;'>\n";
    foreach ($errors as $error) {
        echo "<li>$error</li>\n";
    }
    echo "</ul>\n";
    echo "</div>\n";
}

echo "<h3>Detailed Log</h3>\n";
echo "<ul>\n";
foreach ($syncLog as $logEntry) {
    echo "<li>$logEntry</li>\n";
}
echo "</ul>\n";

echo "<h3>Database Statistics</h3>\n";
echo "<ul>\n";
echo "<li>Total tables: " . count($currentTables) . "</li>\n";

// Get total record counts
$totalRecords = 0;
foreach (['members', 'contacts', 'email_logs', 'email_templates'] as $table) {
    if (in_array($table, $currentTables)) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM `$table`");
        $count = $stmt->fetch()['count'];
        echo "<li>$table: $count records</li>\n";
        $totalRecords += $count;
    }
}
echo "<li>Total key records: $totalRecords</li>\n";
echo "</ul>\n";

?>
