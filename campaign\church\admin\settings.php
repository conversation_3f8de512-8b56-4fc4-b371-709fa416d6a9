<?php
session_start();

require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $pdo->beginTransaction();
        
        // Collect all settings from form
        $allSettings = [];
        
        // General Settings
        if (isset($_POST['general'])) {
            $allSettings = array_merge($allSettings, [
                'site_title' => $_POST['site_title'] ?? '',
                'admin_title' => $_POST['admin_title'] ?? '',
                'site_description' => $_POST['site_description'] ?? '',
                'site_keywords' => $_POST['site_keywords'] ?? '',
                'organization_type' => $_POST['organization_type'] ?? 'church',
                'organization_name' => $_POST['organization_name'] ?? '',
                'organization_mission' => $_POST['organization_mission'] ?? '',
                'organization_vision' => $_POST['organization_vision'] ?? '',
                'organization_values' => $_POST['organization_values'] ?? '',
                'member_term' => $_POST['member_term'] ?? 'Member',
                'leader_term' => $_POST['leader_term'] ?? 'Pastor',
                'group_term' => $_POST['group_term'] ?? 'Ministry',
                'event_term' => $_POST['event_term'] ?? 'Service',
                'donation_term' => $_POST['donation_term'] ?? 'Offering',
                'footer_text' => $_POST['footer_text'] ?? ''
            ]);
        }
        
        // Contact Information
        if (isset($_POST['contact'])) {
            $allSettings = array_merge($allSettings, [
                'contact_phone' => $_POST['contact_phone'] ?? '',
                'contact_email' => $_POST['contact_email'] ?? '',
                'contact_address' => $_POST['contact_address'] ?? '',
                'contact_city' => $_POST['contact_city'] ?? '',
                'contact_state' => $_POST['contact_state'] ?? '',
                'contact_zip' => $_POST['contact_zip'] ?? '',
                'contact_country' => $_POST['contact_country'] ?? '',
                'office_hours' => $_POST['office_hours'] ?? '',
                'emergency_contact' => $_POST['emergency_contact'] ?? ''
            ]);
        }
        
        // Social Media
        if (isset($_POST['social'])) {
            $allSettings = array_merge($allSettings, [
                'facebook_url' => $_POST['facebook_url'] ?? '',
                'twitter_url' => $_POST['twitter_url'] ?? '',
                'instagram_url' => $_POST['instagram_url'] ?? '',
                'youtube_url' => $_POST['youtube_url'] ?? '',
                'linkedin_url' => $_POST['linkedin_url'] ?? '',
                'tiktok_url' => $_POST['tiktok_url'] ?? '',
                'website_url' => $_POST['website_url'] ?? '',
                'blog_url' => $_POST['blog_url'] ?? ''
            ]);
        }
        
        // Email Configuration
        if (isset($_POST['email'])) {
            $allSettings = array_merge($allSettings, [
                'smtp_host' => $_POST['smtp_host'] ?? '',
                'smtp_port' => $_POST['smtp_port'] ?? '587',
                'smtp_username' => $_POST['smtp_username'] ?? '',
                'smtp_password' => $_POST['smtp_password'] ?? '',
                'smtp_encryption' => $_POST['smtp_encryption'] ?? 'tls',
                'from_email' => $_POST['from_email'] ?? '',
                'from_name' => $_POST['from_name'] ?? '',
                'reply_to_email' => $_POST['reply_to_email'] ?? '',
                'email_signature' => $_POST['email_signature'] ?? '',
                'enable_email_queue' => isset($_POST['enable_email_queue']) ? '1' : '0'
            ]);
        }
        
        // System Preferences
        if (isset($_POST['system'])) {
            $allSettings = array_merge($allSettings, [
                'timezone' => $_POST['timezone'] ?? 'America/New_York',
                'date_format' => $_POST['date_format'] ?? 'Y-m-d',
                'time_format' => $_POST['time_format'] ?? 'H:i',
                'currency_symbol' => $_POST['currency_symbol'] ?? '$',
                'currency_code' => $_POST['currency_code'] ?? 'USD',
                'language' => $_POST['language'] ?? 'en',
                'items_per_page' => $_POST['items_per_page'] ?? '25',
                'session_timeout' => $_POST['session_timeout'] ?? '3600',
                'max_upload_size' => $_POST['max_upload_size'] ?? '10',
                'backup_retention_days' => $_POST['backup_retention_days'] ?? '30'
            ]);
        }
        
        // Security Settings (Comprehensive security settings managed in security_settings.php)
        // Basic security toggles removed to prevent duplication
        
        // Notification Settings (Basic system notifications only - email templates managed separately)
        if (isset($_POST['notifications'])) {
            $allSettings = array_merge($allSettings, [
                'enable_event_reminders' => isset($_POST['enable_event_reminders']) ? '1' : '0',
                'event_reminder_days' => $_POST['event_reminder_days'] ?? '3',
                'enable_membership_expiry_alerts' => isset($_POST['enable_membership_expiry_alerts']) ? '1' : '0',
                'membership_expiry_days' => $_POST['membership_expiry_days'] ?? '30',
                'enable_admin_notifications' => isset($_POST['enable_admin_notifications']) ? '1' : '0',
                'admin_notification_email' => $_POST['admin_notification_email'] ?? '',
                'notification_frequency' => $_POST['notification_frequency'] ?? 'daily'
            ]);
        }
        
        // Integration Settings
        if (isset($_POST['integrations'])) {
            $allSettings = array_merge($allSettings, [
                'google_analytics_id' => $_POST['google_analytics_id'] ?? '',
                'facebook_pixel_id' => $_POST['facebook_pixel_id'] ?? '',
                'google_maps_api_key' => $_POST['google_maps_api_key'] ?? '',
                'whatsapp_api_token' => $_POST['whatsapp_api_token'] ?? '',
                'sms_api_key' => $_POST['sms_api_key'] ?? '',
                'payment_gateway' => $_POST['payment_gateway'] ?? 'stripe',
                'stripe_public_key' => $_POST['stripe_public_key'] ?? '',
                'stripe_secret_key' => $_POST['stripe_secret_key'] ?? '',
                'paypal_client_id' => $_POST['paypal_client_id'] ?? '',
                'enable_api_access' => isset($_POST['enable_api_access']) ? '1' : '0'
            ]);
        }
        
        // Save all settings to unified settings table
        foreach ($allSettings as $key => $value) {
            update_site_setting($key, $value);
        }
        
        $pdo->commit();
        $success_message = "Settings updated successfully!";
        
    } catch (Exception $e) {
        $pdo->rollback();
        $error_message = "Error updating settings: " . $e->getMessage();
    }
}

// Get current settings
function getCurrentSettings() {
    $settingKeys = [
        // General
        'site_title', 'admin_title', 'site_description', 'site_keywords', 'organization_type',
        'organization_name', 'organization_mission', 'organization_vision', 'organization_values',
        'member_term', 'leader_term', 'group_term', 'event_term', 'donation_term', 'footer_text',
        // Contact
        'contact_phone', 'contact_email', 'contact_address', 'contact_city', 'contact_state',
        'contact_zip', 'contact_country', 'office_hours', 'emergency_contact',
        // Social Media
        'facebook_url', 'twitter_url', 'instagram_url', 'youtube_url', 'linkedin_url',
        'tiktok_url', 'website_url', 'blog_url',
        // Email
        'smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_encryption',
        'from_email', 'from_name', 'reply_to_email', 'email_signature', 'enable_email_queue',
        // System
        'timezone', 'date_format', 'time_format', 'currency_symbol', 'currency_code',
        'language', 'items_per_page', 'session_timeout', 'max_upload_size', 'backup_retention_days',
        // Security (Comprehensive security settings managed in security_settings.php)
        // Notifications (Basic system notifications - email templates managed in automated_email_templates.php)
        'enable_event_reminders', 'event_reminder_days', 'enable_membership_expiry_alerts',
        'membership_expiry_days', 'enable_admin_notifications', 'admin_notification_email', 'notification_frequency',
        // Integrations
        'google_analytics_id', 'facebook_pixel_id', 'google_maps_api_key', 'whatsapp_api_token',
        'sms_api_key', 'payment_gateway', 'stripe_public_key', 'stripe_secret_key',
        'paypal_client_id', 'enable_api_access'
    ];
    
    $settings = [];
    foreach ($settingKeys as $key) {
        $settings[$key] = get_site_setting($key, '');
    }
    
    return $settings;
}

$currentSettings = getCurrentSettings();

// Page title and header info
$page_title = "Settings";
$page_header = "Settings";
$page_description = "Configure your church management system settings.";

include 'includes/header.php';
?>

            <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Settings Navigation Tabs -->
            <ul class="nav nav-tabs mb-4" id="settingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                        <i class="bi bi-house"></i> General
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab">
                        <i class="bi bi-telephone"></i> Contact
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="social-tab" data-bs-toggle="tab" data-bs-target="#social" type="button" role="tab">
                        <i class="bi bi-share"></i> Social Media
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="email-tab" data-bs-toggle="tab" data-bs-target="#email" type="button" role="tab">
                        <i class="bi bi-envelope"></i> Email
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                        <i class="bi bi-cpu"></i> System
                    </button>
                </li>
                <!-- Security tab removed - managed in security_settings.php -->
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="notifications-tab" data-bs-toggle="tab" data-bs-target="#notifications" type="button" role="tab">
                        <i class="bi bi-bell"></i> Notifications
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="integrations-tab" data-bs-toggle="tab" data-bs-target="#integrations" type="button" role="tab">
                        <i class="bi bi-puzzle"></i> Integrations
                    </button>
                </li>
            </ul>

            <form method="POST" action="">
                <div class="tab-content" id="settingsTabContent">

                    <!-- General Settings Tab -->
                    <div class="tab-pane fade show active" id="general" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-house"></i> General Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="site_title" class="form-label">Site Title</label>
                                            <input type="text" class="form-control" id="site_title" name="site_title"
                                                   value="<?php echo htmlspecialchars($currentSettings['site_title']); ?>" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="admin_title" class="form-label">Admin Panel Title</label>
                                            <input type="text" class="form-control" id="admin_title" name="admin_title"
                                                   value="<?php echo htmlspecialchars($currentSettings['admin_title']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label for="organization_type" class="form-label">Organization Type</label>
                                            <select class="form-select" id="organization_type" name="organization_type">
                                                <option value="church" <?php echo $currentSettings['organization_type'] === 'church' ? 'selected' : ''; ?>>Church</option>
                                                <option value="nonprofit" <?php echo $currentSettings['organization_type'] === 'nonprofit' ? 'selected' : ''; ?>>Non-Profit</option>
                                                <option value="ministry" <?php echo $currentSettings['organization_type'] === 'ministry' ? 'selected' : ''; ?>>Ministry</option>
                                                <option value="community" <?php echo $currentSettings['organization_type'] === 'community' ? 'selected' : ''; ?>>Community Group</option>
                                                <option value="other" <?php echo $currentSettings['organization_type'] === 'other' ? 'selected' : ''; ?>>Other</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="organization_name" class="form-label">Organization Name</label>
                                            <input type="text" class="form-control" id="organization_name" name="organization_name"
                                                   value="<?php echo htmlspecialchars($currentSettings['organization_name']); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="site_description" class="form-label">Site Description</label>
                                            <textarea class="form-control" id="site_description" name="site_description" rows="3"><?php echo htmlspecialchars($currentSettings['site_description']); ?></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label for="site_keywords" class="form-label">Site Keywords</label>
                                            <input type="text" class="form-control" id="site_keywords" name="site_keywords"
                                                   value="<?php echo htmlspecialchars($currentSettings['site_keywords']); ?>"
                                                   placeholder="keyword1, keyword2, keyword3">
                                        </div>
                                        <div class="mb-3">
                                            <label for="footer_text" class="form-label">Footer Text</label>
                                            <textarea class="form-control" id="footer_text" name="footer_text" rows="2"><?php echo htmlspecialchars($currentSettings['footer_text']); ?></textarea>
                                        </div>
                                    </div>
                                </div>

                                <hr>
                                <h6><i class="bi bi-tags"></i> Organization Terminology</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="member_term" class="form-label">Member Term</label>
                                            <input type="text" class="form-control" id="member_term" name="member_term"
                                                   value="<?php echo htmlspecialchars($currentSettings['member_term']); ?>" placeholder="Member">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="leader_term" class="form-label">Leader Term</label>
                                            <input type="text" class="form-control" id="leader_term" name="leader_term"
                                                   value="<?php echo htmlspecialchars($currentSettings['leader_term']); ?>" placeholder="Pastor">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="group_term" class="form-label">Group Term</label>
                                            <input type="text" class="form-control" id="group_term" name="group_term"
                                                   value="<?php echo htmlspecialchars($currentSettings['group_term']); ?>" placeholder="Ministry">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="event_term" class="form-label">Event Term</label>
                                            <input type="text" class="form-control" id="event_term" name="event_term"
                                                   value="<?php echo htmlspecialchars($currentSettings['event_term']); ?>" placeholder="Service">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="donation_term" class="form-label">Donation Term</label>
                                            <input type="text" class="form-control" id="donation_term" name="donation_term"
                                                   value="<?php echo htmlspecialchars($currentSettings['donation_term']); ?>" placeholder="Offering">
                                        </div>
                                    </div>
                                </div>

                                <hr>
                                <h6><i class="bi bi-card-text"></i> Mission & Vision</h6>
                                <div class="mb-3">
                                    <label for="organization_mission" class="form-label">Mission Statement</label>
                                    <textarea class="form-control" id="organization_mission" name="organization_mission" rows="3"><?php echo htmlspecialchars($currentSettings['organization_mission']); ?></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="organization_vision" class="form-label">Vision Statement</label>
                                    <textarea class="form-control" id="organization_vision" name="organization_vision" rows="3"><?php echo htmlspecialchars($currentSettings['organization_vision']); ?></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="organization_values" class="form-label">Core Values</label>
                                    <textarea class="form-control" id="organization_values" name="organization_values" rows="4"><?php echo htmlspecialchars($currentSettings['organization_values']); ?></textarea>
                                </div>

                                <button type="submit" name="general" class="btn btn-primary">
                                    <i class="bi bi-save"></i> Save General Settings
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information Tab -->
                    <div class="tab-pane fade" id="contact" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-telephone"></i> Contact Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="contact_phone" class="form-label">Phone Number</label>
                                            <input type="tel" class="form-control" id="contact_phone" name="contact_phone"
                                                   value="<?php echo htmlspecialchars($currentSettings['contact_phone']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label for="contact_email" class="form-label">Contact Email</label>
                                            <input type="email" class="form-control" id="contact_email" name="contact_email"
                                                   value="<?php echo htmlspecialchars($currentSettings['contact_email']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label for="emergency_contact" class="form-label">Emergency Contact</label>
                                            <input type="text" class="form-control" id="emergency_contact" name="emergency_contact"
                                                   value="<?php echo htmlspecialchars($currentSettings['emergency_contact']); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="contact_address" class="form-label">Street Address</label>
                                            <input type="text" class="form-control" id="contact_address" name="contact_address"
                                                   value="<?php echo htmlspecialchars($currentSettings['contact_address']); ?>">
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="contact_city" class="form-label">City</label>
                                                    <input type="text" class="form-control" id="contact_city" name="contact_city"
                                                           value="<?php echo htmlspecialchars($currentSettings['contact_city']); ?>">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="contact_state" class="form-label">State/Province</label>
                                                    <input type="text" class="form-control" id="contact_state" name="contact_state"
                                                           value="<?php echo htmlspecialchars($currentSettings['contact_state']); ?>">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="contact_zip" class="form-label">ZIP/Postal Code</label>
                                                    <input type="text" class="form-control" id="contact_zip" name="contact_zip"
                                                           value="<?php echo htmlspecialchars($currentSettings['contact_zip']); ?>">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="contact_country" class="form-label">Country</label>
                                                    <input type="text" class="form-control" id="contact_country" name="contact_country"
                                                           value="<?php echo htmlspecialchars($currentSettings['contact_country']); ?>">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="office_hours" class="form-label">Office Hours</label>
                                    <textarea class="form-control" id="office_hours" name="office_hours" rows="3"
                                              placeholder="Monday - Friday: 9:00 AM - 5:00 PM"><?php echo htmlspecialchars($currentSettings['office_hours']); ?></textarea>
                                </div>

                                <button type="submit" name="contact" class="btn btn-primary">
                                    <i class="bi bi-save"></i> Save Contact Information
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Social Media Tab -->
                    <div class="tab-pane fade" id="social" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-share"></i> Social Media Links</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="facebook_url" class="form-label">
                                                <i class="bi bi-facebook"></i> Facebook URL
                                            </label>
                                            <input type="url" class="form-control" id="facebook_url" name="facebook_url"
                                                   value="<?php echo htmlspecialchars($currentSettings['facebook_url']); ?>"
                                                   placeholder="https://facebook.com/yourpage">
                                        </div>
                                        <div class="mb-3">
                                            <label for="twitter_url" class="form-label">
                                                <i class="bi bi-twitter"></i> Twitter URL
                                            </label>
                                            <input type="url" class="form-control" id="twitter_url" name="twitter_url"
                                                   value="<?php echo htmlspecialchars($currentSettings['twitter_url']); ?>"
                                                   placeholder="https://twitter.com/yourhandle">
                                        </div>
                                        <div class="mb-3">
                                            <label for="instagram_url" class="form-label">
                                                <i class="bi bi-instagram"></i> Instagram URL
                                            </label>
                                            <input type="url" class="form-control" id="instagram_url" name="instagram_url"
                                                   value="<?php echo htmlspecialchars($currentSettings['instagram_url']); ?>"
                                                   placeholder="https://instagram.com/yourhandle">
                                        </div>
                                        <div class="mb-3">
                                            <label for="youtube_url" class="form-label">
                                                <i class="bi bi-youtube"></i> YouTube URL
                                            </label>
                                            <input type="url" class="form-control" id="youtube_url" name="youtube_url"
                                                   value="<?php echo htmlspecialchars($currentSettings['youtube_url']); ?>"
                                                   placeholder="https://youtube.com/yourchannel">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="linkedin_url" class="form-label">
                                                <i class="bi bi-linkedin"></i> LinkedIn URL
                                            </label>
                                            <input type="url" class="form-control" id="linkedin_url" name="linkedin_url"
                                                   value="<?php echo htmlspecialchars($currentSettings['linkedin_url']); ?>"
                                                   placeholder="https://linkedin.com/company/yourcompany">
                                        </div>
                                        <div class="mb-3">
                                            <label for="tiktok_url" class="form-label">
                                                <i class="bi bi-tiktok"></i> TikTok URL
                                            </label>
                                            <input type="url" class="form-control" id="tiktok_url" name="tiktok_url"
                                                   value="<?php echo htmlspecialchars($currentSettings['tiktok_url']); ?>"
                                                   placeholder="https://tiktok.com/@yourhandle">
                                        </div>
                                        <div class="mb-3">
                                            <label for="website_url" class="form-label">
                                                <i class="bi bi-globe"></i> Website URL
                                            </label>
                                            <input type="url" class="form-control" id="website_url" name="website_url"
                                                   value="<?php echo htmlspecialchars($currentSettings['website_url']); ?>"
                                                   placeholder="https://yourwebsite.com">
                                        </div>
                                        <div class="mb-3">
                                            <label for="blog_url" class="form-label">
                                                <i class="bi bi-journal-text"></i> Blog URL
                                            </label>
                                            <input type="url" class="form-control" id="blog_url" name="blog_url"
                                                   value="<?php echo htmlspecialchars($currentSettings['blog_url']); ?>"
                                                   placeholder="https://yourblog.com">
                                        </div>
                                    </div>
                                </div>

                                <button type="submit" name="social" class="btn btn-primary">
                                    <i class="bi bi-save"></i> Save Social Media Settings
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Email Configuration Tab -->
                    <div class="tab-pane fade" id="email" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-envelope"></i> Email Configuration</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="smtp_host" class="form-label">SMTP Host</label>
                                            <input type="text" class="form-control" id="smtp_host" name="smtp_host"
                                                   value="<?php echo htmlspecialchars($currentSettings['smtp_host']); ?>"
                                                   placeholder="smtp.gmail.com">
                                        </div>
                                        <div class="mb-3">
                                            <label for="smtp_port" class="form-label">SMTP Port</label>
                                            <input type="number" class="form-control" id="smtp_port" name="smtp_port"
                                                   value="<?php echo htmlspecialchars($currentSettings['smtp_port']); ?>"
                                                   placeholder="587">
                                        </div>
                                        <div class="mb-3">
                                            <label for="smtp_encryption" class="form-label">Encryption</label>
                                            <select class="form-select" id="smtp_encryption" name="smtp_encryption">
                                                <option value="tls" <?php echo $currentSettings['smtp_encryption'] === 'tls' ? 'selected' : ''; ?>>TLS</option>
                                                <option value="ssl" <?php echo $currentSettings['smtp_encryption'] === 'ssl' ? 'selected' : ''; ?>>SSL</option>
                                                <option value="none" <?php echo $currentSettings['smtp_encryption'] === 'none' ? 'selected' : ''; ?>>None</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="smtp_username" class="form-label">SMTP Username</label>
                                            <input type="text" class="form-control" id="smtp_username" name="smtp_username"
                                                   value="<?php echo htmlspecialchars($currentSettings['smtp_username']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label for="smtp_password" class="form-label">SMTP Password</label>
                                            <input type="password" class="form-control" id="smtp_password" name="smtp_password"
                                                   value="<?php echo htmlspecialchars($currentSettings['smtp_password']); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="from_email" class="form-label">From Email</label>
                                            <input type="email" class="form-control" id="from_email" name="from_email"
                                                   value="<?php echo htmlspecialchars($currentSettings['from_email']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label for="from_name" class="form-label">From Name</label>
                                            <input type="text" class="form-control" id="from_name" name="from_name"
                                                   value="<?php echo htmlspecialchars($currentSettings['from_name']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label for="reply_to_email" class="form-label">Reply-To Email</label>
                                            <input type="email" class="form-control" id="reply_to_email" name="reply_to_email"
                                                   value="<?php echo htmlspecialchars($currentSettings['reply_to_email']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label for="email_signature" class="form-label">Email Signature</label>
                                            <textarea class="form-control" id="email_signature" name="email_signature" rows="3"><?php echo htmlspecialchars($currentSettings['email_signature']); ?></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="enable_email_queue" name="enable_email_queue"
                                                       <?php echo $currentSettings['enable_email_queue'] === '1' ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="enable_email_queue">
                                                    Enable Email Queue
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <button type="submit" name="email" class="btn btn-primary">
                                    <i class="bi bi-save"></i> Save Email Settings
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- System Preferences Tab -->
                    <div class="tab-pane fade" id="system" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-cpu"></i> System Preferences</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="timezone" class="form-label">Timezone</label>
                                            <select class="form-select" id="timezone" name="timezone">
                                                <option value="America/New_York" <?php echo $currentSettings['timezone'] === 'America/New_York' ? 'selected' : ''; ?>>Eastern Time</option>
                                                <option value="America/Chicago" <?php echo $currentSettings['timezone'] === 'America/Chicago' ? 'selected' : ''; ?>>Central Time</option>
                                                <option value="America/Denver" <?php echo $currentSettings['timezone'] === 'America/Denver' ? 'selected' : ''; ?>>Mountain Time</option>
                                                <option value="America/Los_Angeles" <?php echo $currentSettings['timezone'] === 'America/Los_Angeles' ? 'selected' : ''; ?>>Pacific Time</option>
                                                <option value="UTC" <?php echo $currentSettings['timezone'] === 'UTC' ? 'selected' : ''; ?>>UTC</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="date_format" class="form-label">Date Format</label>
                                            <select class="form-select" id="date_format" name="date_format">
                                                <option value="Y-m-d" <?php echo $currentSettings['date_format'] === 'Y-m-d' ? 'selected' : ''; ?>>YYYY-MM-DD</option>
                                                <option value="m/d/Y" <?php echo $currentSettings['date_format'] === 'm/d/Y' ? 'selected' : ''; ?>>MM/DD/YYYY</option>
                                                <option value="d/m/Y" <?php echo $currentSettings['date_format'] === 'd/m/Y' ? 'selected' : ''; ?>>DD/MM/YYYY</option>
                                                <option value="F j, Y" <?php echo $currentSettings['date_format'] === 'F j, Y' ? 'selected' : ''; ?>>Month DD, YYYY</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="time_format" class="form-label">Time Format</label>
                                            <select class="form-select" id="time_format" name="time_format">
                                                <option value="H:i" <?php echo $currentSettings['time_format'] === 'H:i' ? 'selected' : ''; ?>>24-hour (HH:MM)</option>
                                                <option value="g:i A" <?php echo $currentSettings['time_format'] === 'g:i A' ? 'selected' : ''; ?>>12-hour (H:MM AM/PM)</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="currency_symbol" class="form-label">Currency Symbol</label>
                                            <input type="text" class="form-control" id="currency_symbol" name="currency_symbol"
                                                   value="<?php echo htmlspecialchars($currentSettings['currency_symbol']); ?>" maxlength="5">
                                        </div>
                                        <div class="mb-3">
                                            <label for="currency_code" class="form-label">Currency Code</label>
                                            <input type="text" class="form-control" id="currency_code" name="currency_code"
                                                   value="<?php echo htmlspecialchars($currentSettings['currency_code']); ?>" maxlength="3">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="language" class="form-label">Language</label>
                                            <select class="form-select" id="language" name="language">
                                                <option value="en" <?php echo $currentSettings['language'] === 'en' ? 'selected' : ''; ?>>English</option>
                                                <option value="es" <?php echo $currentSettings['language'] === 'es' ? 'selected' : ''; ?>>Spanish</option>
                                                <option value="fr" <?php echo $currentSettings['language'] === 'fr' ? 'selected' : ''; ?>>French</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="items_per_page" class="form-label">Items Per Page</label>
                                            <select class="form-select" id="items_per_page" name="items_per_page">
                                                <option value="10" <?php echo $currentSettings['items_per_page'] === '10' ? 'selected' : ''; ?>>10</option>
                                                <option value="25" <?php echo $currentSettings['items_per_page'] === '25' ? 'selected' : ''; ?>>25</option>
                                                <option value="50" <?php echo $currentSettings['items_per_page'] === '50' ? 'selected' : ''; ?>>50</option>
                                                <option value="100" <?php echo $currentSettings['items_per_page'] === '100' ? 'selected' : ''; ?>>100</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="session_timeout" class="form-label">Session Timeout (seconds)</label>
                                            <input type="number" class="form-control" id="session_timeout" name="session_timeout"
                                                   value="<?php echo htmlspecialchars($currentSettings['session_timeout']); ?>" min="300" max="86400">
                                        </div>
                                        <div class="mb-3">
                                            <label for="max_upload_size" class="form-label">Max Upload Size (MB)</label>
                                            <input type="number" class="form-control" id="max_upload_size" name="max_upload_size"
                                                   value="<?php echo htmlspecialchars($currentSettings['max_upload_size']); ?>" min="1" max="100">
                                        </div>
                                        <div class="mb-3">
                                            <label for="backup_retention_days" class="form-label">Backup Retention (days)</label>
                                            <input type="number" class="form-control" id="backup_retention_days" name="backup_retention_days"
                                                   value="<?php echo htmlspecialchars($currentSettings['backup_retention_days']); ?>" min="1" max="365">
                                        </div>
                                    </div>
                                </div>

                                <button type="submit" name="system" class="btn btn-primary">
                                    <i class="bi bi-save"></i> Save System Settings
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Security Settings Tab removed - comprehensive security settings managed in security_settings.php -->

                    <!-- Notifications Tab -->
                    <div class="tab-pane fade" id="notifications" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-bell"></i> System Notification Settings</h5>
                                <p class="text-muted mb-0">Basic system notifications. For email template management, visit <a href="automated_email_templates.php">Email Templates</a>.</p>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle"></i>
                                    <strong>Note:</strong> Birthday email templates and detailed notification settings are managed in
                                    <a href="automated_email_templates.php" class="alert-link">Automated Email Templates</a>.
                                    For comprehensive security settings including password policies, login security, and audit logging, visit
                                    <a href="security_settings.php" class="alert-link">Security Settings</a>.
                                    This section covers general system notifications only.
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="enable_event_reminders" name="enable_event_reminders"
                                                       <?php echo $currentSettings['enable_event_reminders'] === '1' ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="enable_event_reminders">
                                                    Enable Event Reminders
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="event_reminder_days" class="form-label">Event Reminder Days Ahead</label>
                                            <input type="number" class="form-control" id="event_reminder_days" name="event_reminder_days"
                                                   value="<?php echo htmlspecialchars($currentSettings['event_reminder_days']); ?>" min="1" max="14">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="enable_membership_expiry_alerts" name="enable_membership_expiry_alerts"
                                                       <?php echo $currentSettings['enable_membership_expiry_alerts'] === '1' ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="enable_membership_expiry_alerts">
                                                    Enable Membership Expiry Alerts
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="membership_expiry_days" class="form-label">Membership Expiry Alert Days</label>
                                            <input type="number" class="form-control" id="membership_expiry_days" name="membership_expiry_days"
                                                   value="<?php echo htmlspecialchars($currentSettings['membership_expiry_days']); ?>" min="7" max="90">
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="enable_admin_notifications" name="enable_admin_notifications"
                                                       <?php echo $currentSettings['enable_admin_notifications'] === '1' ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="enable_admin_notifications">
                                                    Enable Admin Notifications
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="admin_notification_email" class="form-label">Admin Notification Email</label>
                                            <input type="email" class="form-control" id="admin_notification_email" name="admin_notification_email"
                                                   value="<?php echo htmlspecialchars($currentSettings['admin_notification_email']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label for="notification_frequency" class="form-label">Notification Frequency</label>
                                            <select class="form-select" id="notification_frequency" name="notification_frequency">
                                                <option value="immediate" <?php echo $currentSettings['notification_frequency'] === 'immediate' ? 'selected' : ''; ?>>Immediate</option>
                                                <option value="daily" <?php echo $currentSettings['notification_frequency'] === 'daily' ? 'selected' : ''; ?>>Daily</option>
                                                <option value="weekly" <?php echo $currentSettings['notification_frequency'] === 'weekly' ? 'selected' : ''; ?>>Weekly</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <button type="submit" name="notifications" class="btn btn-primary">
                                    <i class="bi bi-save"></i> Save Notification Settings
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Integrations Tab -->
                    <div class="tab-pane fade" id="integrations" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-puzzle"></i> Third-Party Integrations</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Analytics & Tracking</h6>
                                        <div class="mb-3">
                                            <label for="google_analytics_id" class="form-label">Google Analytics ID</label>
                                            <input type="text" class="form-control" id="google_analytics_id" name="google_analytics_id"
                                                   value="<?php echo htmlspecialchars($currentSettings['google_analytics_id']); ?>"
                                                   placeholder="GA-XXXXXXXXX-X">
                                        </div>
                                        <div class="mb-3">
                                            <label for="facebook_pixel_id" class="form-label">Facebook Pixel ID</label>
                                            <input type="text" class="form-control" id="facebook_pixel_id" name="facebook_pixel_id"
                                                   value="<?php echo htmlspecialchars($currentSettings['facebook_pixel_id']); ?>">
                                        </div>

                                        <h6>Maps & Location</h6>
                                        <div class="mb-3">
                                            <label for="google_maps_api_key" class="form-label">Google Maps API Key</label>
                                            <input type="text" class="form-control" id="google_maps_api_key" name="google_maps_api_key"
                                                   value="<?php echo htmlspecialchars($currentSettings['google_maps_api_key']); ?>">
                                        </div>

                                        <h6>Communication</h6>
                                        <div class="mb-3">
                                            <label for="whatsapp_api_token" class="form-label">WhatsApp API Token</label>
                                            <input type="text" class="form-control" id="whatsapp_api_token" name="whatsapp_api_token"
                                                   value="<?php echo htmlspecialchars($currentSettings['whatsapp_api_token']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label for="sms_api_key" class="form-label">SMS API Key</label>
                                            <input type="text" class="form-control" id="sms_api_key" name="sms_api_key"
                                                   value="<?php echo htmlspecialchars($currentSettings['sms_api_key']); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Payment Processing</h6>
                                        <div class="mb-3">
                                            <label for="payment_gateway" class="form-label">Primary Payment Gateway</label>
                                            <select class="form-select" id="payment_gateway" name="payment_gateway">
                                                <option value="stripe" <?php echo $currentSettings['payment_gateway'] === 'stripe' ? 'selected' : ''; ?>>Stripe</option>
                                                <option value="paypal" <?php echo $currentSettings['payment_gateway'] === 'paypal' ? 'selected' : ''; ?>>PayPal</option>
                                                <option value="both" <?php echo $currentSettings['payment_gateway'] === 'both' ? 'selected' : ''; ?>>Both</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="stripe_public_key" class="form-label">Stripe Public Key</label>
                                            <input type="text" class="form-control" id="stripe_public_key" name="stripe_public_key"
                                                   value="<?php echo htmlspecialchars($currentSettings['stripe_public_key']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label for="stripe_secret_key" class="form-label">Stripe Secret Key</label>
                                            <input type="password" class="form-control" id="stripe_secret_key" name="stripe_secret_key"
                                                   value="<?php echo htmlspecialchars($currentSettings['stripe_secret_key']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label for="paypal_client_id" class="form-label">PayPal Client ID</label>
                                            <input type="text" class="form-control" id="paypal_client_id" name="paypal_client_id"
                                                   value="<?php echo htmlspecialchars($currentSettings['paypal_client_id']); ?>">
                                        </div>

                                        <h6>API Access</h6>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="enable_api_access" name="enable_api_access"
                                                       <?php echo $currentSettings['enable_api_access'] === '1' ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="enable_api_access">
                                                    Enable API Access
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <button type="submit" name="integrations" class="btn btn-primary">
                                    <i class="bi bi-save"></i> Save Integration Settings
                                </button>
                            </div>
                        </div>
                    </div>

                </div>
            </form>


<script>
// Tab persistence
document.addEventListener('DOMContentLoaded', function() {
    // Get the last active tab from localStorage
    const lastActiveTab = localStorage.getItem('settingsActiveTab');
    if (lastActiveTab) {
        const tabButton = document.querySelector(`#${lastActiveTab}`);
        if (tabButton) {
            const tab = new bootstrap.Tab(tabButton);
            tab.show();
        }
    }

    // Save active tab to localStorage
    const tabButtons = document.querySelectorAll('#settingsTabs button[data-bs-toggle="tab"]');
    tabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', function(e) {
            localStorage.setItem('settingsActiveTab', e.target.id);
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>

                    <!-- Social Media Tab -->
                    <div class="tab-pane fade" id="social" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-share"></i> Social Media Links</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="facebook_url" class="form-label">
                                                <i class="bi bi-facebook"></i> Facebook URL
                                            </label>
                                            <input type="url" class="form-control" id="facebook_url" name="facebook_url"
                                                   value="<?php echo htmlspecialchars($currentSettings['facebook_url']); ?>"
                                                   placeholder="https://facebook.com/yourpage">
                                        </div>
                                        <div class="mb-3">
                                            <label for="twitter_url" class="form-label">
                                                <i class="bi bi-twitter"></i> Twitter URL
                                            </label>
                                            <input type="url" class="form-control" id="twitter_url" name="twitter_url"
                                                   value="<?php echo htmlspecialchars($currentSettings['twitter_url']); ?>"
                                                   placeholder="https://twitter.com/yourhandle">
                                        </div>
                                        <div class="mb-3">
                                            <label for="instagram_url" class="form-label">
                                                <i class="bi bi-instagram"></i> Instagram URL
                                            </label>
                                            <input type="url" class="form-control" id="instagram_url" name="instagram_url"
                                                   value="<?php echo htmlspecialchars($currentSettings['instagram_url']); ?>"
                                                   placeholder="https://instagram.com/yourhandle">
                                        </div>
                                        <div class="mb-3">
                                            <label for="youtube_url" class="form-label">
                                                <i class="bi bi-youtube"></i> YouTube URL
                                            </label>
                                            <input type="url" class="form-control" id="youtube_url" name="youtube_url"
                                                   value="<?php echo htmlspecialchars($currentSettings['youtube_url']); ?>"
                                                   placeholder="https://youtube.com/yourchannel">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="linkedin_url" class="form-label">
                                                <i class="bi bi-linkedin"></i> LinkedIn URL
                                            </label>
                                            <input type="url" class="form-control" id="linkedin_url" name="linkedin_url"
                                                   value="<?php echo htmlspecialchars($currentSettings['linkedin_url']); ?>"
                                                   placeholder="https://linkedin.com/company/yourcompany">
                                        </div>
                                        <div class="mb-3">
                                            <label for="tiktok_url" class="form-label">
                                                <i class="bi bi-tiktok"></i> TikTok URL
                                            </label>
                                            <input type="url" class="form-control" id="tiktok_url" name="tiktok_url"
                                                   value="<?php echo htmlspecialchars($currentSettings['tiktok_url']); ?>"
                                                   placeholder="https://tiktok.com/@yourhandle">
                                        </div>
                                        <div class="mb-3">
                                            <label for="website_url" class="form-label">
                                                <i class="bi bi-globe"></i> Website URL
                                            </label>
                                            <input type="url" class="form-control" id="website_url" name="website_url"
                                                   value="<?php echo htmlspecialchars($currentSettings['website_url']); ?>"
                                                   placeholder="https://yourwebsite.com">
                                        </div>
                                        <div class="mb-3">
                                            <label for="blog_url" class="form-label">
                                                <i class="bi bi-journal-text"></i> Blog URL
                                            </label>
                                            <input type="url" class="form-control" id="blog_url" name="blog_url"
                                                   value="<?php echo htmlspecialchars($currentSettings['blog_url']); ?>"
                                                   placeholder="https://yourblog.com">
                                        </div>
                                    </div>
                                </div>

                                <button type="submit" name="social" class="btn btn-primary">
                                    <i class="bi bi-save"></i> Save Social Media Settings
                                </button>
                            </div>
                        </div>
                    </div>
