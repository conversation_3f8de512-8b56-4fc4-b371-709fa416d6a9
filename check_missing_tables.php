<?php
/**
 * Check for missing database tables required by admin pages
 */

require_once 'campaign/church/config.php';

echo "<h1>Missing Tables Check</h1>\n";

try {
    // Get all existing tables
    $stmt = $pdo->query("SHOW TABLES");
    $existingTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h2>Existing Tables (" . count($existingTables) . ")</h2>\n";
    echo "<ul>\n";
    foreach ($existingTables as $table) {
        echo "<li>$table</li>\n";
    }
    echo "</ul>\n";
    
    // Check for required tables
    $requiredTables = [
        'custom_field_definitions' => 'Required for custom_fields.php',
        'custom_field_values' => 'Required for custom_fields.php',
        'custom_field_permissions' => 'Required for custom_fields.php',
        'appearance_settings' => 'Required for logo_upload.php and branding_settings.php'
    ];
    
    echo "<h2>Required Tables Check</h2>\n";
    $missingTables = [];
    
    foreach ($requiredTables as $table => $purpose) {
        if (in_array($table, $existingTables)) {
            echo "<p style='color: green;'>✓ $table - EXISTS ($purpose)</p>\n";
        } else {
            echo "<p style='color: red;'>✗ $table - MISSING ($purpose)</p>\n";
            $missingTables[] = $table;
        }
    }
    
    if (!empty($missingTables)) {
        echo "<h2>Missing Tables Found</h2>\n";
        echo "<p style='color: red;'>The following tables are missing and need to be created:</p>\n";
        echo "<ul>\n";
        foreach ($missingTables as $table) {
            echo "<li style='color: red;'>$table - {$requiredTables[$table]}</li>\n";
        }
        echo "</ul>\n";
        
        echo "<h3>Solution</h3>\n";
        echo "<p>Run the following setup scripts to create the missing tables:</p>\n";
        echo "<ul>\n";
        if (in_array('custom_field_definitions', $missingTables) || 
            in_array('custom_field_values', $missingTables) || 
            in_array('custom_field_permissions', $missingTables)) {
            echo "<li><a href='setup_custom_fields.php'>Setup Custom Fields Tables</a></li>\n";
        }
        if (in_array('appearance_settings', $missingTables)) {
            echo "<li><a href='setup_appearance_settings.php'>Setup Appearance Settings Table</a></li>\n";
        }
        echo "</ul>\n";
    } else {
        echo "<h2 style='color: green;'>✅ All Required Tables Exist</h2>\n";
        echo "<p>All required database tables are present. The admin pages should work correctly.</p>\n";
    }
    
    // Check for admin session
    echo "<h2>Session Check</h2>\n";
    session_start();
    if (isset($_SESSION['admin_id'])) {
        echo "<p style='color: green;'>✓ Admin session active (ID: {$_SESSION['admin_id']})</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠ No admin session found - you may need to log in</p>\n";
    }
    
    // Check includes/header.php
    echo "<h2>File Dependencies Check</h2>\n";
    $headerPath = 'campaign/church/admin/includes/header.php';
    if (file_exists($headerPath)) {
        echo "<p style='color: green;'>✓ includes/header.php exists</p>\n";
    } else {
        echo "<p style='color: red;'>✗ includes/header.php missing at: $headerPath</p>\n";
    }
    
    $footerPath = 'campaign/church/admin/includes/footer.php';
    if (file_exists($footerPath)) {
        echo "<p style='color: green;'>✓ includes/footer.php exists</p>\n";
    } else {
        echo "<p style='color: red;'>✗ includes/footer.php missing at: $footerPath</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>\n";
}

?>
