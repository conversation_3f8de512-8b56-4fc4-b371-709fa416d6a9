<?php
/**
 * Database Synchronization Check Script
 * This script will analyze the current database structure and compare it with expected schema
 */

// Include the config file
require_once 'campaign/church/config.php';

echo "<h1>Database Synchronization Analysis</h1>\n";
echo "<h2>Current Environment: " . (defined('ENVIRONMENT') ? ENVIRONMENT : 'development') . "</h2>\n";

try {
    // Test database connection
    echo "<h3>1. Database Connection Test</h3>\n";
    echo "Host: $host<br>\n";
    echo "Database: $dbname<br>\n";
    echo "Username: $username<br>\n";
    
    $pdo->query("SELECT 1");
    echo "<span style='color: green;'>✓ Database connection successful</span><br>\n";
    
    // Get current database tables
    echo "<h3>2. Current Database Tables</h3>\n";
    $stmt = $pdo->query("SHOW TABLES");
    $currentTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<p>Found " . count($currentTables) . " tables:</p>\n";
    echo "<ul>\n";
    foreach ($currentTables as $table) {
        echo "<li>$table</li>\n";
    }
    echo "</ul>\n";
    
    // Expected tables based on schema analysis
    $expectedTables = [
        'activity_logs',
        'admin_2fa', 
        'admin_activity_logs',
        'admin_login_attempts',
        'admins',
        'automated_emails_settings',
        'calendar_settings',
        'contact_email_logs',
        'contact_group_members',
        'contact_groups',
        'contacts',
        'donation_notifications',
        'donations',
        'email_logs',
        'email_settings',
        'email_templates',
        'email_tracking',
        'members',
        'payment_settings',
        'payment_transactions',
        'security_logs',
        'security_settings',
        'settings',
        'whatsapp_logs',
        'whatsapp_settings',
        'whatsapp_templates'
    ];
    
    echo "<h3>3. Schema Comparison</h3>\n";
    
    // Find missing tables
    $missingTables = array_diff($expectedTables, $currentTables);
    if (!empty($missingTables)) {
        echo "<h4 style='color: red;'>Missing Tables (" . count($missingTables) . "):</h4>\n";
        echo "<ul>\n";
        foreach ($missingTables as $table) {
            echo "<li style='color: red;'>$table</li>\n";
        }
        echo "</ul>\n";
    } else {
        echo "<h4 style='color: green;'>✓ All expected tables exist</h4>\n";
    }
    
    // Find extra tables
    $extraTables = array_diff($currentTables, $expectedTables);
    if (!empty($extraTables)) {
        echo "<h4 style='color: orange;'>Extra Tables (" . count($extraTables) . "):</h4>\n";
        echo "<ul>\n";
        foreach ($extraTables as $table) {
            echo "<li style='color: orange;'>$table</li>\n";
        }
        echo "</ul>\n";
    }
    
    // Check structure of key tables that exist
    echo "<h3>4. Table Structure Analysis</h3>\n";
    
    $keyTables = ['members', 'email_templates', 'email_logs', 'email_tracking', 'contacts'];
    
    foreach ($keyTables as $table) {
        if (in_array($table, $currentTables)) {
            echo "<h4>Table: $table</h4>\n";
            $stmt = $pdo->query("DESCRIBE $table");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<table border='1' style='border-collapse: collapse;'>\n";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td>{$column['Field']}</td>";
                echo "<td>{$column['Type']}</td>";
                echo "<td>{$column['Null']}</td>";
                echo "<td>{$column['Key']}</td>";
                echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
                echo "<td>{$column['Extra']}</td>";
                echo "</tr>\n";
            }
            echo "</table><br>\n";
            
            // Show row count
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch()['count'];
            echo "<p>Row count: $count</p>\n";
        } else {
            echo "<h4 style='color: red;'>Table: $table (MISSING)</h4>\n";
        }
    }
    
    // Check for foreign key constraints
    echo "<h3>5. Foreign Key Constraints Check</h3>\n";
    $stmt = $pdo->query("
        SELECT 
            TABLE_NAME,
            COLUMN_NAME,
            CONSTRAINT_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM 
            INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE 
            REFERENCED_TABLE_SCHEMA = '$dbname' 
            AND REFERENCED_TABLE_NAME IS NOT NULL
    ");
    $foreignKeys = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($foreignKeys)) {
        echo "<table border='1' style='border-collapse: collapse;'>\n";
        echo "<tr><th>Table</th><th>Column</th><th>References</th><th>Constraint</th></tr>\n";
        foreach ($foreignKeys as $fk) {
            echo "<tr>";
            echo "<td>{$fk['TABLE_NAME']}</td>";
            echo "<td>{$fk['COLUMN_NAME']}</td>";
            echo "<td>{$fk['REFERENCED_TABLE_NAME']}.{$fk['REFERENCED_COLUMN_NAME']}</td>";
            echo "<td>{$fk['CONSTRAINT_NAME']}</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<p style='color: orange;'>No foreign key constraints found</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>\n";
    echo "<p>Stack trace:</p><pre>" . $e->getTraceAsString() . "</pre>\n";
}

echo "<h3>6. Next Steps</h3>\n";
echo "<p>Based on this analysis, we can:</p>\n";
echo "<ol>\n";
echo "<li>Create missing tables using the schema definition</li>\n";
echo "<li>Add missing foreign key constraints</li>\n";
echo "<li>Verify data integrity</li>\n";
echo "<li>Test application functionality</li>\n";
echo "</ol>\n";

?>
