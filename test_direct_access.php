<?php
/**
 * Test Direct Access to Admin Pages
 */

// Start session and set admin authentication
session_start();
$_SESSION['admin_id'] = 4;
$_SESSION['admin_username'] = 'admin';
$_SESSION['admin_email'] = '<EMAIL>';

echo "<h1>Testing Direct Access to Admin Pages</h1>\n";

echo "<h2>Session Information</h2>\n";
echo "<ul>\n";
echo "<li>Admin ID: " . ($_SESSION['admin_id'] ?? 'Not set') . "</li>\n";
echo "<li>Admin Username: " . ($_SESSION['admin_username'] ?? 'Not set') . "</li>\n";
echo "<li>Admin Email: " . ($_SESSION['admin_email'] ?? 'Not set') . "</li>\n";
echo "</ul>\n";

echo "<h2>Test Links</h2>\n";
echo "<p>Click these links to test the admin pages:</p>\n";
echo "<ul>\n";
echo "<li><a href='campaign/church/admin/custom_fields.php' target='_blank'>Custom Fields Management</a></li>\n";
echo "<li><a href='campaign/church/admin/logo_upload.php' target='_blank'>Logo Upload</a></li>\n";
echo "<li><a href='campaign/church/admin/branding_settings.php' target='_blank'>Branding Settings</a></li>\n";
echo "</ul>\n";

echo "<h2>Alternative: Direct Include Test</h2>\n";

// Test 1: Custom Fields
echo "<h3>1. Custom Fields Page</h3>\n";
echo "<iframe src='campaign/church/admin/custom_fields.php' width='100%' height='400' style='border: 1px solid #ccc;'></iframe>\n";

// Test 2: Logo Upload
echo "<h3>2. Logo Upload Page</h3>\n";
echo "<iframe src='campaign/church/admin/logo_upload.php' width='100%' height='400' style='border: 1px solid #ccc;'></iframe>\n";

// Test 3: Branding Settings
echo "<h3>3. Branding Settings Page</h3>\n";
echo "<iframe src='campaign/church/admin/branding_settings.php' width='100%' height='400' style='border: 1px solid #ccc;'></iframe>\n";

?>
