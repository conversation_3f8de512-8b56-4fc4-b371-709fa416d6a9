<?php
/**
 * Setup Appearance Settings Table
 * Creates the appearance_settings table required for logo_upload.php and branding_settings.php
 */

require_once 'campaign/church/config.php';

echo "<h1>Appearance Settings Table Setup</h1>\n";

try {
    $pdo->beginTransaction();
    
    // Create appearance_settings table
    $sql = "CREATE TABLE IF NOT EXISTS appearance_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_name VARCHAR(100) NOT NULL UNIQUE,
        setting_value TEXT NULL,
        setting_type ENUM('text', 'number', 'boolean', 'json', 'url', 'color', 'file') DEFAULT 'text',
        category VARCHAR(50) DEFAULT 'general',
        description TEXT NULL,
        is_public BOOLEAN DEFAULT FALSE COMMENT 'Whether setting can be accessed by non-admin users',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_setting_name (setting_name),
        INDEX idx_category (category),
        INDEX idx_is_public (is_public)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<p style='color: green;'>✓ Created appearance_settings table</p>\n";
    
    // Insert default appearance settings
    $defaultSettings = [
        // Branding Settings
        ['organization_name', 'Church Management System', 'text', 'branding', 'Organization/Church name', 1],
        ['organization_type', 'church', 'text', 'branding', 'Type of organization', 1],
        ['tagline', 'Connecting Faith, Building Community', 'text', 'branding', 'Organization tagline', 1],
        ['primary_color', '#007bff', 'color', 'branding', 'Primary brand color', 1],
        ['secondary_color', '#6c757d', 'color', 'branding', 'Secondary brand color', 1],
        ['accent_color', '#28a745', 'color', 'branding', 'Accent color', 1],
        ['font_family', 'Arial, sans-serif', 'text', 'branding', 'Default font family', 1],
        
        // Logo Settings
        ['logo_url', '', 'file', 'logo', 'Main logo file path', 1],
        ['logo_width', '200', 'number', 'logo', 'Logo width in pixels', 0],
        ['logo_height', '80', 'number', 'logo', 'Logo height in pixels', 0],
        ['logo_position', 'left', 'text', 'logo', 'Logo position (left, center, right)', 0],
        ['show_logo_text', '1', 'boolean', 'logo', 'Show organization name with logo', 0],
        ['logo_text', '', 'text', 'logo', 'Custom text to show with logo', 0],
        ['favicon_url', '', 'file', 'logo', 'Favicon file path', 1],
        
        // White Label Settings
        ['hide_powered_by', '0', 'boolean', 'whitelabel', 'Hide "Powered by" text', 0],
        ['custom_footer_text', '', 'text', 'whitelabel', 'Custom footer text', 1],
        ['support_email', '', 'text', 'whitelabel', 'Support contact email', 1],
        ['support_phone', '', 'text', 'whitelabel', 'Support contact phone', 1],
        ['support_url', '', 'url', 'whitelabel', 'Support website URL', 1],
        ['custom_domain', '', 'text', 'whitelabel', 'Custom domain name', 0],
        ['ssl_enabled', '1', 'boolean', 'whitelabel', 'SSL/HTTPS enabled', 0],
        
        // SEO & Analytics
        ['meta_description', 'Church Management System for modern congregations', 'text', 'seo', 'Meta description for SEO', 1],
        ['meta_keywords', 'church, management, members, events, donations', 'text', 'seo', 'Meta keywords for SEO', 1],
        ['google_analytics_id', '', 'text', 'analytics', 'Google Analytics tracking ID', 0],
        ['facebook_pixel_id', '', 'text', 'analytics', 'Facebook Pixel ID', 0],
        
        // Custom Code
        ['custom_css', '', 'text', 'customization', 'Custom CSS styles', 0],
        ['custom_head_code', '', 'text', 'customization', 'Custom HTML for <head> section', 0],
        ['custom_body_code', '', 'text', 'customization', 'Custom HTML for <body> section', 0],
        
        // Terminology Settings
        ['term_members', 'Members', 'text', 'terminology', 'Term for members (plural)', 1],
        ['term_member', 'Member', 'text', 'terminology', 'Term for member (singular)', 1],
        ['term_events', 'Events', 'text', 'terminology', 'Term for events (plural)', 1],
        ['term_event', 'Event', 'text', 'terminology', 'Term for event (singular)', 1],
        ['term_campaigns', 'Campaigns', 'text', 'terminology', 'Term for campaigns (plural)', 1],
        ['term_campaign', 'Campaign', 'text', 'terminology', 'Term for campaign (singular)', 1],
        ['term_groups', 'Groups', 'text', 'terminology', 'Term for groups (plural)', 1],
        ['term_group', 'Group', 'text', 'terminology', 'Term for group (singular)', 1],
        ['term_leaders', 'Leaders', 'text', 'terminology', 'Term for leaders (plural)', 1],
        ['term_leader', 'Leader', 'text', 'terminology', 'Term for leader (singular)', 1],
        ['term_volunteers', 'Volunteers', 'text', 'terminology', 'Term for volunteers (plural)', 1],
        ['term_volunteer', 'Volunteer', 'text', 'terminology', 'Term for volunteer (singular)', 1],
        ['term_donations', 'Donations', 'text', 'terminology', 'Term for donations (plural)', 1],
        ['term_donation', 'Donation', 'text', 'terminology', 'Term for donation (singular)', 1],
        ['term_attendance', 'Attendance', 'text', 'terminology', 'Term for attendance', 1],
        ['term_ministry', 'Ministry', 'text', 'terminology', 'Term for ministry (singular)', 1],
        ['term_ministries', 'Ministries', 'text', 'terminology', 'Term for ministries (plural)', 1]
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO appearance_settings 
        (setting_name, setting_value, setting_type, category, description, is_public, created_at)
        VALUES (?, ?, ?, ?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE
        setting_type = VALUES(setting_type),
        category = VALUES(category),
        description = VALUES(description),
        is_public = VALUES(is_public),
        updated_at = NOW()
    ");
    
    foreach ($defaultSettings as $setting) {
        $stmt->execute($setting);
    }
    
    echo "<p style='color: green;'>✓ Inserted default appearance settings (" . count($defaultSettings) . " settings)</p>\n";
    
    // Create uploads directory for logos
    $uploadsDir = 'campaign/church/uploads/logos';
    if (!is_dir($uploadsDir)) {
        if (mkdir($uploadsDir, 0755, true)) {
            echo "<p style='color: green;'>✓ Created uploads directory: $uploadsDir</p>\n";
        } else {
            echo "<p style='color: orange;'>⚠ Could not create uploads directory: $uploadsDir</p>\n";
        }
    } else {
        echo "<p style='color: green;'>✓ Uploads directory already exists: $uploadsDir</p>\n";
    }
    
    // Create assets directory for processed images
    $assetsDir = 'campaign/church/assets/img/logos';
    if (!is_dir($assetsDir)) {
        if (mkdir($assetsDir, 0755, true)) {
            echo "<p style='color: green;'>✓ Created assets directory: $assetsDir</p>\n";
        } else {
            echo "<p style='color: orange;'>⚠ Could not create assets directory: $assetsDir</p>\n";
        }
    } else {
        echo "<p style='color: green;'>✓ Assets directory already exists: $assetsDir</p>\n";
    }
    
    $pdo->commit();
    echo "\n<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h3 style='color: #155724; margin-top: 0;'>✅ Appearance Settings Setup Complete!</h3>\n";
    echo "<p style='color: #155724;'>The appearance_settings table has been created successfully.</p>\n";
    echo "<p style='color: #155724;'>You can now access:</p>\n";
    echo "<ul style='color: #155724;'>\n";
    echo "<li><a href='campaign/church/admin/logo_upload.php'>Logo Upload Management</a></li>\n";
    echo "<li><a href='campaign/church/admin/branding_settings.php'>Branding Settings</a></li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    $pdo->rollback();
    echo "<p style='color: red;'>❌ Error setting up appearance settings: " . $e->getMessage() . "</p>\n";
    exit(1);
}
?>
